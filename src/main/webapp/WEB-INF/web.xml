<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://java.sun.com/xml/ns/javaee"
         xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd"
         id="WebApp_ID" version="3.0">

    <context-param>
        <param-name>contextConfigLocation</param-name>
        <param-value>classpath:applicationContext.xml</param-value>
    </context-param>


    <listener>
        <listener-class>org.springframework.web.context.request.RequestContextListener</listener-class>
    </listener>

    <listener>
        <listener-class>com.dfire.dubbo.listener.DubboShutdownListener</listener-class>
    </listener>

    <!-- 字符集 过滤器 -->
    <filter>
        <filter-name>CharacterEncodingFilter</filter-name>
        <filter-class>org.springframework.web.filter.CharacterEncodingFilter</filter-class>
        <init-param>
            <param-name>encoding</param-name>
            <param-value>UTF-8</param-value>
        </init-param>
        <init-param>
            <param-name>forceEncoding</param-name>
            <param-value>true</param-value>
        </init-param>
    </filter>
    <filter-mapping>
        <filter-name>CharacterEncodingFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
    <!--<filter>-->
    <!--<filter-name>springSessionRepositoryFilter</filter-name>-->
    <!--<filter-class>org.springframework.web.filter.DelegatingFilterProxy</filter-class>-->
    <!--</filter>-->
    <!--<filter-mapping>-->
    <!--<filter-name>springSessionRepositoryFilter</filter-name>-->
    <!--<url-pattern>/*</url-pattern>-->
    <!--</filter-mapping>-->

    <!-- Spring的log4j监听器 -->
    <listener>
        <listener-class>org.springframework.web.util.Log4jConfigListener</listener-class>
    </listener>

    <servlet>
        <servlet-name>wechat</servlet-name>
        <servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
        <init-param>
            <description>spring mvc 配置文件</description>
            <param-name>contextConfigLocation</param-name>
            <param-value>classpath:wechat-servlet.xml</param-value>
        </init-param>
        <init-param>
            <param-name>throwExceptionIfNoHandlerFound</param-name>
            <param-value>true</param-value>
        </init-param>
        <load-on-startup>1</load-on-startup>
    </servlet>
    <servlet-mapping>
        <servlet-name>wechat</servlet-name>
        <url-pattern>/*</url-pattern>
    </servlet-mapping>

    <!--数据压缩-->
    <filter>
        <filter-name>CompressingFilter</filter-name>
        <filter-class>com.github.ziplet.filter.compression.CompressingFilter</filter-class>
    </filter>

    <filter-mapping>
        <filter-name>CompressingFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <!--跨域问题过滤OPTIONS请求-->
    <filter>
        <filter-name>CORS</filter-name>
        <filter-class>com.thetransactioncompany.cors.CORSFilter</filter-class>
    </filter>

    <filter-mapping>
        <filter-name>CORS</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <!-- 通用过滤器 -->
    <filter>
        <filter-name>common-filter</filter-name>
        <filter-class>com.twodfire.wechat.common.filter.CommonFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>common-filter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <!-- sentinel过滤器 -->
    <!--<filter>-->
        <!--<filter-name>sentinelCommonFilter</filter-name>-->
        <!--<filter-class>com.alibaba.csp.sentinel.adapter.servlet.CommonFilter</filter-class>-->
    <!--</filter>-->
    <!--<filter-mapping>-->
        <!--<filter-name>sentinelCommonFilter</filter-name>-->
        <!--<url-pattern>/*</url-pattern>-->
    <!--</filter-mapping>-->

    <!-- 域名拦截 -->
    <filter>
        <filter-name>domain-filter</filter-name>
        <filter-class>com.twodfire.wechat.common.filter.DomainFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>domain-filter</filter-name>
        <url-pattern>/bind/v1/get_code</url-pattern>
    </filter-mapping>

    <!-- 国际化 -->
    <filter>
        <filter-name>language-filter</filter-name>
        <filter-class>com.twodfire.wechat.common.filter.MultiLanguageFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>language-filter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>


    <welcome-file-list>
        <welcome-file>index.jsp</welcome-file>
    </welcome-file-list>

    <!--<jsp-config>-->
    <!--<jsp-property-group>-->
    <!--<url-pattern>*.jsp</url-pattern>-->
    <!--<trim-directive-whitespaces>true</trim-directive-whitespaces>-->
    <!--</jsp-property-group>-->
    <!--</jsp-config>-->

    <!-- 页面请求异常处理 -->
    <!--<error-page>-->
    <!--<error-code>404</error-code>-->
    <!--<location>/page/error-return.html?code=404</location>-->
    <!--</error-page>-->
    <!--<error-page>-->
    <!--<error-code>500</error-code>-->
    <!--<location>/page/error-return.html?code=500</location>-->
    <!--</error-page>-->
    <!--<error-page>-->
    <!--<error-code>403</error-code>-->
    <!--<location>/WEB-INF/jsp/error/error.jsp</location>-->
    <!--</error-page>-->
    <!--<error-page>-->
    <!--<error-code>404</error-code>-->
    <!--<location>/WEB-INF/jsp/error/error.jsp</location>-->
    <!--</error-page>-->
    <!--<error-page>-->
    <!--<error-code>405</error-code>-->
    <!--<location>/WEB-INF/jsp/error/error.jsp</location>-->
    <!--</error-page>-->
    <!--<error-page>-->
    <!--<error-code>503</error-code>-->
    <!--<location>/WEB-INF/jsp/error/error.jsp</location>-->
    <!--</error-page>-->
    <!--<error-page>-->
    <!--<exception-type>java.lang.NullPointerException</exception-type>-->
    <!--<location>/WEB-INF/jsp/error/error.jsp</location>-->
    <!--</error-page>-->
    <!--<error-page>-->
    <!--<exception-type>java.lang.Exception</exception-type>-->
    <!--<location>/WEB-INF/jsp/error/error.jsp</location>-->
    <!--</error-page>-->
    <!--<error-page>-->
    <!--<exception-type>-->
    <!--javax.servlet.ServletException-->
    <!--</exception-type >-->
    <!--<location>/WEB-INF/jsp/error/error.jsp</location>-->
    <!--</error-page>-->

    <!--<error-page>-->
    <!--<exception-type>java.io.IOException</exception-type >-->
    <!--<location>/WEB-INF/jsp/error/error.jsp</location>-->
    <!--</error-page>-->
    <!--<error-page>-->
    <!--<exception-type>java.lang.Throwable</exception-type >-->
    <!--<location>/WEB-INF/jsp/error/error.jsp</location>-->
    <!--</error-page>-->

</web-app>
