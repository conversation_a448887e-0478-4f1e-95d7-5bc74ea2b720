/*
 * Copyright (C) 2009-2016 Hangzhou 2Dfire Technology Co., Ltd. All rights reserved
 */
package com.twodfire.wechat.props;

/**
 * AuthUrlProperties
 *
 * <AUTHOR>
 * @since 2017-07-05
 */
public class FireMemberUrlProperties {
    /**
     * vip充值店铺
     */
    private String vipEntityId;
    /**
     * 火会员：比一比
     */
    private String fireMemberCompareUrl;

    /**
     * 火会员首页
     */
    private String fireMemberFirstPageCallBackUrl;
    /**
     * 成就分享详情
     */
    private String fireMemberAchievementInfoUrl;

    /**
     * 会员充值页
     */
    private String fireMemberRechargeVipUrl;

    public String getFireMemberCompareUrl() {
        return fireMemberCompareUrl;
    }

    public void setFireMemberCompareUrl(String fireMemberCompareUrl) {
        this.fireMemberCompareUrl = fireMemberCompareUrl;
    }

    public String getFireMemberFirstPageCallBackUrl() {
        return fireMemberFirstPageCallBackUrl;
    }

    public void setFireMemberFirstPageCallBackUrl(String fireMemberFirstPageCallBackUrl) {
        this.fireMemberFirstPageCallBackUrl = fireMemberFirstPageCallBackUrl;
    }

    public String getFireMemberAchievementInfoUrl() {
        return fireMemberAchievementInfoUrl;
    }

    public void setFireMemberAchievementInfoUrl(String fireMemberAchievementInfoUrl) {
        this.fireMemberAchievementInfoUrl = fireMemberAchievementInfoUrl;
    }

    public String getFireMemberRechargeVipUrl() {
        return fireMemberRechargeVipUrl;
    }

    public void setFireMemberRechargeVipUrl(String fireMemberRechargeVipUrl) {
        this.fireMemberRechargeVipUrl = fireMemberRechargeVipUrl;
    }

    public String getVipEntityId() {
        return vipEntityId;
    }

    public void setVipEntityId(String vipEntityId) {
        this.vipEntityId = vipEntityId;
    }
}
