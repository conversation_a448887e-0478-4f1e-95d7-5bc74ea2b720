/*
 * Copyright (C) 2009-2016 Hangzhou 2Dfire Technology Co., Ltd. All rights reserved
 */
package com.twodfire.wechat.props;

/**
 * 零售店铺配置信息
 *
 * <AUTHOR>
 * @since 2018-06-12
 */
public class RetailShopProperties {
    /**
     * 店铺分享原oauth返回的前端跳转url
     */
    private String originalShareCallbackUrl;
    /**
     * 分享前端跳转url
     */
    private String shareCallbackUrl;

    public String getOriginalShareCallbackUrl() {
        return originalShareCallbackUrl;
    }

    public void setOriginalShareCallbackUrl(String originalShareCallbackUrl) {
        this.originalShareCallbackUrl = originalShareCallbackUrl;
    }

    public String getShareCallbackUrl() {
        return shareCallbackUrl;
    }

    public void setShareCallbackUrl(String shareCallbackUrl) {
        this.shareCallbackUrl = shareCallbackUrl;
    }
}