/*
 * Copyright (C) 2009-2017 Hangzhou 2Dfire Technology Co., Ltd.All rights reserved
 */
package com.twodfire.wechat.props;

/**
 * AlbumDetailProperties
 *
 * <AUTHOR>
 * @since 2018-08-13
 */
public class AlbumDetailProperties {
    private String albumDetailUrl;

    public String getAlbumDetailUrl() {
        return albumDetailUrl;
    }

    public void setAlbumDetailUrl(String albumDetailUrl) {
        this.albumDetailUrl = albumDetailUrl;
    }
}
