/**
 * ==============================================================================
 * Copyright (c) 2016 by www.2dfire.com, All rights reserved.
 * ==============================================================================
 * This software is the confidential and proprietary information of
 * 2dfire.com, Inc. ("Confidential Information"). You shall not disclose
 * such Confidential Information and shall use it only in accordance
 * with the terms of the license agreement you entered into with 2dfire.com, Inc.
 * ------------------------------------------------------------------------------
 * File name:  PageSplitUrl.java
 * Author: qiezi
 * Date: 2016/11/25 15:01
 * Description:
 * Nothing.
 * Function List:
 * 1. Nothing.
 * History:
 * 1. Nothing.
 * ==============================================================================
 */
package com.twodfire.wechat.props;

import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;
import org.springframework.stereotype.Component;

import java.io.Serializable;

/**
 * User: qiezi
 * Date: 2016/11/25
 * Time: 15:01
 * Description:
 */
@Component
public class PageSplitUrl implements Serializable {

    private static final long serialVersionUID = -7207740977220556346L;

    private String pageSplitUrl;
    private String pageSplitPrefix;
    /**
     * 会员中心
     */
    private String userCenter;

    public String getPageSplitUrl() {
        return pageSplitUrl;
    }

    public void setPageSplitUrl(String pageSplitUrl) {
        this.pageSplitUrl = pageSplitUrl;
    }

    public String getPageSplitPrefix() {
        return pageSplitPrefix;
    }

    public void setPageSplitPrefix(String pageSplitPrefix) {
        this.pageSplitPrefix = pageSplitPrefix;
    }

    public String getUserCenter() {
        return userCenter;
    }

    public void setUserCenter(String userCenter) {
        this.userCenter = userCenter;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SIMPLE_STYLE);
    }
}
