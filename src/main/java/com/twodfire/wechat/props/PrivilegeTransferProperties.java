/*
 * Copyright (C) 2009-2016 Hangzhou 2Dfire Technology Co., Ltd. All rights reserved
 */
package com.twodfire.wechat.props;

/**
 * PrivilegeTransferProperties
 *
 * <AUTHOR>
 * @since 2017-05-03
 */
public class PrivilegeTransferProperties {
    private String privilegeTransferCallbackUrl;

    public String getPrivilegeTransferCallbackUrl() {
        return privilegeTransferCallbackUrl;
    }

    public void setPrivilegeTransferCallbackUrl(String privilegeTransferCallbackUrl) {
        this.privilegeTransferCallbackUrl = privilegeTransferCallbackUrl;
    }
}