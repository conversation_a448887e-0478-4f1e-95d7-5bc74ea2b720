package com.twodfire.wechat.enums.miniprogram;

/**
 * <pre>
 *     采用7位编码  前2位为业务部门编码 中间两位为业务编码  最后三位递增
 *   部门编码
 *      10:小二部门;11:掌柜部门;12:收银部门;13:交易部门;14:营销部门;
 *   业务编码
 *      10: 公共相关;11: 小二相关;12: 商品相关;13: 店铺相关;14: 订单相关
 *      15: 交易相关;16: 收银相关;17: 会员相关;18: 优惠相关;19: 二维码相关
 *      20: 授权相关;21: 购物车相关;22:菜单相关;
 * </pre>
 */
public enum MiniProgramBizCodeEnum {

    /**
     * 公共相关业务码
     */
    RESPONSE_IS_NULL_EXCEPTION("1010002", "服务未响应,请稍后重试! "),


    /**
     * 二维码相关业务码
     */
    QR_CODE_NOT_SUPPORTED_EXCEPTION("1019001", "暂时不支持解析该码"),
    QR_CODE_ENTITY_ID_IS_NULL_EXCEPTION("1019002", "二维码解析的店铺ID为空"),
    QR_CODE_NOT_IS_THIS_ENTITY("1019003", "当前不支持该二维码"),
    QR_CODE_NOT_SUPPORT_THIS_CONDITION("1019004", "当前不支持该二维码"),
    QR_CODE_EXPIRE("1019005", "二维码已过期");

    private String code;

    private String message;

    MiniProgramBizCodeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
