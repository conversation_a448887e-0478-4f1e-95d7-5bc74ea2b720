package com.twodfire.wechat.service;

import com.dfire.pay.bill.domain.PayBill;
import com.dfire.soa.consumer.vo.PayBillStatusVo;
import com.twodfire.share.result.Result;

/**
 * User: robin
 * Email: <EMAIL>
 * Date: 16/1/22
 * Time: AM10:32
 * Desc:
 */
public interface IWechatBillService {

    /**
     * 查询微信流水
     *
     * @return
     */
    Result<PayBillStatusVo> queryBill(String customerId, int payType, int clientType, String entityId, String outTradeNo, String
            snapshotId, String extraSnapshotId, String cardId, int source, String extraType, String rechargeOrderId, Integer memberJoinSource, Integer pollNum
            , String itemId, String activityEntityId, String inviteCustomerId);

    /**
     * 根据订单号查询订单
     *
     * @param orderId
     * @param entityId
     * @return
     */
    Result<PayBill> queryBillWithOrderId(String orderId, String entityId);

    Result queryBillWithMch(String subMchId, String outTradeNo);
}
