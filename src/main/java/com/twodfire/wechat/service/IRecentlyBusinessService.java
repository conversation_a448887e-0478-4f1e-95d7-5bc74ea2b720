package com.twodfire.wechat.service;

import com.twodfire.wechat.dto.RecentlyBuyItemDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @title:
 * @description: TODO
 * @date 2024-12-31 2:42 p.m.
 */
public interface IRecentlyBusinessService {


    /**
     * 是否存在最近购买的数据
     * @param entityId 门店id
     * @param multiMenuId 菜单id
     * @param customerId 用户id
     * @return
     */
    boolean existRecentlyBuyItem(String entityId,String multiMenuId,String customerId);


    /**
     * 获取最近购买的商品
     * @param entityId
     * @param multiMenuId
     * @param customerId
     * @return
     */
    List<RecentlyBuyItemDTO> getRecentlyBuyItem(String entityId, String multiMenuId, String customerId);


}
