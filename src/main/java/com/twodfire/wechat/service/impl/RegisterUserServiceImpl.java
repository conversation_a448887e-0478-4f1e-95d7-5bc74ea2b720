package com.twodfire.wechat.service.impl;

import com.dfire.consumer.i18n.code.api.WeixinMealCode;
import com.dfire.consumer.util.LoggerUtil;
import com.dfire.soa.member.bean.ThirdPartyMember;
import com.dfire.soa.member.bean.TwodfireMember;
import com.dfire.soa.member.em.EnumThirdType;
import com.dfire.soa.member.service.ICustomerMobileService;
import com.dfire.soa.member.service.IThirdPartyMemberService;
import com.dfire.soa.member.service.ITwodfireMemberService;
import com.dfire.soa.oauth.common.domain.SessionUserInfo;
import com.twodfire.share.result.Result;
import com.twodfire.share.util.ApiResultUtil;
import com.twodfire.share.util.ResultUtil;
import com.twodfire.wechat.api.camera.ErrorMessageEnum;
import com.twodfire.wechat.bean.CustomerRegister;
import com.twodfire.wechat.common.local.RequestManager;
import com.twodfire.wechat.common.logger.LoggerMarkers;
import com.twodfire.wechat.common.logger.WXLoggerFactory;
import com.twodfire.wechat.helper.SessionHelper;
import com.twodfire.wechat.service.IRegisterUserService;
import com.twodfire.wechat.utils.FastJsonUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * User: robin
 * Email: <EMAIL>
 * Date: 15/5/15
 * Time: AM10:19
 * Desc: 会员服务
 */
@Service
public class RegisterUserServiceImpl implements IRegisterUserService {

    @Resource
    IThirdPartyMemberService thirdPartyMemberService;
    @Resource
    SessionHelper sessionHelper;
    @Resource
    ICustomerMobileService customerMobileService;
    @Resource
    ITwodfireMemberService twodfireMemberService;

    /**
     * 获取sessionUserInfo中当前的客户端用户类型
     *
     * @param sessionUserInfo
     * @return
     */
    private int getUserType(SessionUserInfo sessionUserInfo) {
        int userType = EnumThirdType.WECHAT.getType();
        if (sessionUserInfo != null && sessionUserInfo.getType() > 0) {
            userType = sessionUserInfo.getType();
        }
        return userType;
    }

    private CustomerRegister getCustomerRegister(ThirdPartyMember thirdPartyMember) {
        if (null == thirdPartyMember) {
            return null;
        }
        CustomerRegister user = new CustomerRegister();
        user.setId(thirdPartyMember.getCustomerRegisterId());
        user.setNickname(thirdPartyMember.getNickName());
        user.setUrl(thirdPartyMember.getUrl());
        user.setSex(thirdPartyMember.getSex());
        TwodfireMember twodfireMember = thirdPartyMember.getTwodfireMember();
        if (null != twodfireMember) {
            user.setMobile(twodfireMember.getMobile());
        }
        return user;
    }

    @Override
    public CustomerRegister getRegisterUserByCustomerRegisterId(String id) throws Exception {
        try {
            SessionUserInfo sessionUserInfo = sessionHelper.getSessionUserInfo(RequestManager.getHttpServletRequest());
//            Result<CustomerRegisterThirdPartyPojo> crtResult = memberThirdPartyService.queryCustomerRegisterByCustomerRegisterId(id, userType);
            Result<ThirdPartyMember> crtResult = thirdPartyMemberService.queryInfoIncludeTwodfireMemberByTwodfireMemberId(id, sessionUserInfo.getType());
            if (crtResult.isSuccess()) {
                return getCustomerRegister(crtResult.getModel());
            } else {
                LoggerUtil.error(WXLoggerFactory.OAUTH_LOGGER, LoggerMarkers.GET_REGISTER_USER_ERROR,
                        FastJsonUtils.convMsg(new String[][]{
                                {"customerRegisterId", id},
                                {"userType", sessionUserInfo.getType() + ""},
                                {"resultCode", crtResult.getResultCode()}
                        }));
                return null;
            }
        } catch (Exception ex) {
            String errorMsg = ex.getMessage();
            LoggerUtil.error(WXLoggerFactory.OAUTH_LOGGER, LoggerMarkers.GET_REGISTER_USER_ERROR,
                    FastJsonUtils.convMsg(new String[][]{{"customerRegisterId", id}, {"ex", errorMsg}
                    }));
        }
        return null;
    }

    /**
     * 通过unionId和用户类型获取会员的信息
     *
     * @param unionId
     * @param userType
     * @return
     * @throws Exception
     */
    @Override
    public CustomerRegister getRegisterUserByUnionIdAndType(String unionId, short userType) {
        try {
            if (StringUtils.isBlank(unionId)) {
                return null;
            }
//            Result<CustomerRegisterThirdPartyPojo> result = memberThirdPartyService.queryCustomerRegisterByThirdPartyId(unionId, userType);
            Result<ThirdPartyMember> result = thirdPartyMemberService.queryInfoIncludeTwodfireMemberByThirdPartyId(unionId, userType);
            if (ResultUtil.isModelNotNull(result)) {
                ThirdPartyMember thirdPartyMember = result.getModel();
                return getCustomerRegister(thirdPartyMember);
            } else {
                LoggerUtil.warn(WXLoggerFactory.OAUTH_LOGGER, LoggerMarkers.GET_REGISTER_USER_ERROR,
                        FastJsonUtils.convMsg(new String[][]{
                                {"unionId", unionId},
                                {"userType", userType + ""},
                                {"resultCode", result.getResultCode()}
                        }));
                return null;
            }
        } catch (Exception e) {
            LoggerUtil.warn(WXLoggerFactory.OAUTH_LOGGER, LoggerMarkers.GET_REGISTER_USER_ERROR,
                    FastJsonUtils.convMsg(new String[][]{
                            {"unionId", unionId},
                            {"userType", userType + ""}
                    }), e);
        }

        return null;
    }

    @Override
    public Result<String> registerUser(String unionid, String avatar, String nickname, String mobile, String sex, int type) throws Exception {
        if (StringUtils.isBlank(nickname)) {
            nickname = WeixinMealCode.MULTI_WMI109.getMultiCode();
        }
//        Result result = memberThirdPartyService.registeNoValidateMobile(mobile, unionid, avatar, nickname, sex, type);
        //该接口已经废弃 kibana未查到调用
        Result result = thirdPartyMemberService.registeNoValidateMobile(mobile, "+86", unionid, avatar, nickname, sex, type);
        if (result.isSuccess()) {
            String msg = FastJsonUtils.convMsg(new String[][]{
                    {"unionid", unionid},
                    {"avatar", avatar},
                    {"nickname", nickname},
                    {"mobile", mobile},
                    {"sex", sex}}, "用户注册成功");
            LoggerUtil.info(WXLoggerFactory.BUSINESS_LOGGER, LoggerMarkers.REGISTER_USER, msg);
        } else {
            String msg = FastJsonUtils.convMsg(new String[][]{
                    {"unionid", unionid},
                    {"avatar", avatar},
                    {"nickname", nickname},
                    {"mobile", mobile},
                    {"sex", sex},
                    {"msg", result.getMessage()}}, "用户注册失败");
            LoggerUtil.error(WXLoggerFactory.BUSINESS_LOGGER, LoggerMarkers.REGISTER_USER_ERROR, msg);
        }
        return result;
    }

    @Override
    public Result<Boolean> isCustomerVerified(String customerId) {
        try {
            return customerMobileService.isCustomerVerified(customerId);
        } catch (Exception ex) {
            LoggerUtil.error(WXLoggerFactory.BUSINESS_LOGGER, LoggerMarkers.REGISTER_USER_ERROR,
                    FastJsonUtils.convMsg(new String[][]{
                                    {"customerId", customerId},
                                    {"message", String.valueOf(ex.getMessage())}},
                            "判断当前用户是否绑定过手机号出错"));
        }
        return null;
    }

    @Override
    public boolean isMobileRegistered(String areaCode, String mobile) {
        Result<TwodfireMember> result = twodfireMemberService.queryInfoByMobile(mobile, areaCode);
        if (ResultUtil.isModelNotNull(result)) {
            return true;
        }
        return false;
    }

    @Override
    public ErrorMessageEnum checkUserCanJoinSystemMember(SessionUserInfo currentSession) {

        // 校验当前的session中手机号是否为800,当前手机号是800 则没授权过
        String session_mobile = currentSession.getMobile();
        if (session_mobile.startsWith("800")) {
            return ErrorMessageEnum.MULTI_WMI260;
        }

        return null;
    }
}
