/**
 *
 */
package com.twodfire.wechat.service.impl.support;

import com.dfire.pay.center.constants.PaySource;
import com.dfire.pay.constants.PayClientType;
import com.dfire.pay.constants.PayNotifyMsgTag;
import com.dfire.soa.oauth.common.constants.ThirdPartyUAType;
import com.twodfire.wechat.utils.HttpRequestUtils;

import javax.servlet.http.HttpServletRequest;

import static com.google.common.base.Preconditions.checkNotNull;

/**
 * <AUTHOR> 2017-6-28 上午11:27:56
 */
public class GeneralPayRequests {

    public enum PayBizType {
        //会员卡业务
        MEMBER_CARD,
        //外卖业务
        TACK_OUT,
        //兑换券业务
        COUPON,
        //正常点餐
        GENERAL_MEAL,
        //购买火会员vip
        FIRE_MEMBER_VIP,
        //购买礼品业务
        BUY_GIFTS,
        //企业卡充值
        ENTERPRISE_CARD_RECHARGE,
        ;

        public static boolean isSupportModule(PayBizType inputBizType) {
            checkNotNull(inputBizType, "inputBizType null");
            if (PayBizType.BUY_GIFTS.equals(inputBizType)) {
                return false;
            }
            return true;
        }
    }

    /**
     * 创建通用的AppPayRequest
     * @param payBizType
     * @param httpRequest
     * @return
     */
    public static GeneralPayRequest createGeneralAppPayRequest(PayBizType payBizType, HttpServletRequest httpRequest, ThirdPartyUAType uaType) {
    	GeneralPayRequest generalPayRequest = null;
    	 switch (payBizType) {
	         case MEMBER_CARD:
	             generalPayRequest = createGeneralAppRequest4MemberCard(httpRequest, uaType);
	             break;
	         case COUPON:
	             generalPayRequest = createGeneralAppPayRequest4Coupon(httpRequest, uaType);
	             break;
	         case TACK_OUT:
	             generalPayRequest = createGeneralAppPayRequest4TakeOut(httpRequest, uaType);
	             break;
	         case GENERAL_MEAL:
	             generalPayRequest = createGeneralAppPayRequest4GeneralMeal(httpRequest, uaType);
	             break;
	         case BUY_GIFTS:
	             generalPayRequest = createGeneralAppPayRequest4BuyGifts(httpRequest, uaType);
	             break;
	         case FIRE_MEMBER_VIP:
	             generalPayRequest = createGeneralAppPayRequest4BuyVIP(httpRequest, uaType);
	             break;
             case ENTERPRISE_CARD_RECHARGE:
                 generalPayRequest = GeneralPayRequests.createGeneralAppPayRequestRechargeEnterpriseCard(httpRequest, uaType);
                 break;
	         default:
	             throw new IllegalArgumentException("Not support payBizType [" + payBizType + "]");
    	 }

    	 return generalPayRequest;
    }

    /**
     * 创建通用的支付请求
     *
     * @param httpRequest
     * @param messageTag
     * @return
     */
    private static GeneralPayRequest createGeneralPayRequest(HttpServletRequest httpRequest, String messageTag, ThirdPartyUAType uaType) {
        checkNotNull(messageTag, "messageTag null");
        PayClientType payClientType;
        if (HttpRequestUtils.requestFromAlipayScanCode(httpRequest)) {
            payClientType = PayClientType.ALIPAY;
        } else if (HttpRequestUtils.requestFromWechatScanCode(httpRequest)) {
            payClientType = PayClientType.WECHAT;
        } else if (HttpRequestUtils.requestFromQQScanCode(httpRequest)) {
            payClientType = PayClientType.QQWALLET;
        } else if (HttpRequestUtils.requestFromDingTalkScanCode(httpRequest)) {
            payClientType = PayClientType.ALIPAY;
        } else if (HttpRequestUtils.requestFromCloudPayScanCode(httpRequest)) {
            payClientType = PayClientType.ALLIN;
        } else if (HttpRequestUtils.requestFromWaiterApp(httpRequest) || HttpRequestUtils.requestFromThirdApp(uaType)) {
            payClientType = PayClientType.WECHAT;
        } else {
            throw new IllegalArgumentException("Not know HttpServletRequest header[userAgent]->[" + httpRequest.getHeader(HttpRequestUtils.USER_AGENT) + "]");
        }
        return new GeneralPayRequest(messageTag, payClientType, PaySource.WAITER);
    }

    /**
     * 创建会员卡业务的支付请求
     *
     * @param httpRequest
     * @return
     */
    private static GeneralPayRequest createGeneralAppRequest4MemberCard(HttpServletRequest httpRequest, ThirdPartyUAType uaType) {
        String msgTag;
        if (HttpRequestUtils.requestFromAlipayScanCode(httpRequest)) {
            msgTag = PayNotifyMsgTag.ALIPAY_MEMBER_CARD_RECHARGE;
        } else if (HttpRequestUtils.requestFromWechatScanCode(httpRequest)) {
            msgTag = PayNotifyMsgTag.WECHAT_MEMBER_CARD_RECHARGE;
        } else if (HttpRequestUtils.requestFromQQScanCode(httpRequest)) {
            msgTag = PayNotifyMsgTag.QQ_MEMBER_CARD_RECHARGE;
        } else if (HttpRequestUtils.requestFromDingTalkScanCode(httpRequest)) {
            msgTag = PayNotifyMsgTag.ALIPAY_MEMBER_CARD_RECHARGE;
        } else if (HttpRequestUtils.requestFromApp(uaType)) {
            msgTag = PayNotifyMsgTag.SCENE_WT_MC_RG_APP_TAG;
        } else {
            throw new IllegalArgumentException("Not know HttpServletRequest header[userAgent]->[" + httpRequest.getHeader(HttpRequestUtils.USER_AGENT) + "]");
        }
        return createGeneralPayRequest(httpRequest, msgTag, uaType);
    }

    /**
     * 创建外卖业务的支付请求
     *
     * @param httpRequest
     * @return
     */
    private static GeneralPayRequest createGeneralAppPayRequest4TakeOut(HttpServletRequest httpRequest, ThirdPartyUAType uaType) {
        String msgTag;
        if (HttpRequestUtils.requestFromAlipayScanCode(httpRequest)) {
            msgTag = PayNotifyMsgTag.ALIPAY_JS_PAY_TAG;
        } else if (HttpRequestUtils.requestFromWechatScanCode(httpRequest)) {
            msgTag = PayNotifyMsgTag.WECHAT_JS_PAY_TAG;
        } else if (HttpRequestUtils.requestFromQQScanCode(httpRequest)) {
            msgTag = PayNotifyMsgTag.QQ_JS_PAY_TAG;
        } else if (HttpRequestUtils.requestFromWaiterApp(httpRequest)) {
            msgTag = PayNotifyMsgTag.WECHAT_APP_PAY_TAG;
        } else {
            throw new IllegalArgumentException("Not know HttpServletRequest header[userAgent]->[" + httpRequest.getHeader(HttpRequestUtils.USER_AGENT) + "]");
        }
        return createGeneralPayRequest(httpRequest, msgTag, uaType);
    }

    /**
     * 创建兑换券业务的支付请求
     *
     * @param httpRequest
     * @return
     */
    private static GeneralPayRequest createGeneralAppPayRequest4Coupon(HttpServletRequest httpRequest, ThirdPartyUAType uaType) {
        String msgTag;
        if (HttpRequestUtils.requestFromAlipayScanCode(httpRequest)) {
            msgTag = PayNotifyMsgTag.ALIPAY_SINGLE_COUPON_PAY_TAG;
        } else if (HttpRequestUtils.requestFromWechatScanCode(httpRequest)) {
            msgTag = PayNotifyMsgTag.WECHAT_SINGLE_COUPON_PAY_TAG;
        } else if (HttpRequestUtils.requestFromQQScanCode(httpRequest)) {
            msgTag = PayNotifyMsgTag.QQ_SINGLE_COUPON_PAY_TAG;
        } else if (HttpRequestUtils.requestFromApp(uaType)) {
            msgTag = PayNotifyMsgTag.WECHAT_SINGLE_COUPON_PAY_TAG;
        } else {
            throw new IllegalArgumentException("Not know HttpServletRequest header[userAgent]->[" + httpRequest.getHeader(HttpRequestUtils.USER_AGENT) + "]");
        }
        return createGeneralPayRequest(httpRequest, msgTag, uaType);
    }


    /**
     * 创建正常点餐业务的支付请求
     *
     * @param httpRequest
     * @return
     */
    private static GeneralPayRequest createGeneralAppPayRequest4GeneralMeal(HttpServletRequest httpRequest, ThirdPartyUAType uaType) {
        String msgTag;
        if (HttpRequestUtils.requestFromAlipayScanCode(httpRequest)) {
            msgTag = PayNotifyMsgTag.ALIPAY_JS_PAY_TAG;
        } else if (HttpRequestUtils.requestFromWechatScanCode(httpRequest)) {
            msgTag = PayNotifyMsgTag.WECHAT_JS_PAY_TAG;
        } else if (HttpRequestUtils.requestFromQQScanCode(httpRequest)) {
            msgTag = PayNotifyMsgTag.QQ_JS_PAY_TAG;
        } else if (HttpRequestUtils.requestFromWaiterApp(httpRequest)) {
            msgTag = PayNotifyMsgTag.WECHAT_APP_PAY_TAG;
        } else {
            throw new IllegalArgumentException("Not know HttpServletRequest header[userAgent]->[" + httpRequest.getHeader(HttpRequestUtils.USER_AGENT) + "]");
        }
        return createGeneralPayRequest(httpRequest, msgTag, uaType);
    }

    /**
     * 创建购买礼品业务的支付请求
     *
     * @param httpRequest
     * @return
     */
    private static GeneralPayRequest createGeneralAppPayRequest4BuyGifts(HttpServletRequest httpRequest, ThirdPartyUAType uaType) {
        String msgTag;
        if (HttpRequestUtils.requestFromAlipayScanCode(httpRequest)) {
            msgTag = PayNotifyMsgTag.ALIPAY_CLICK_LIKE_PAY_TAG;
        } else if (HttpRequestUtils.requestFromWechatScanCode(httpRequest)) {
            msgTag = PayNotifyMsgTag.WECHAT_CLICK_LIKE_PAY_TAG;
        } else if (HttpRequestUtils.requestFromQQScanCode(httpRequest)) {
            msgTag = PayNotifyMsgTag.QQ_CLICK_LIKE_PAY_TAG;
        } else if (HttpRequestUtils.requestFromWaiterApp(httpRequest)) {
            msgTag = PayNotifyMsgTag.WECHAT_CLICK_LIKE_PAY_TAG;
        } else {
            throw new IllegalArgumentException("Not know HttpServletRequest header[userAgent]->[" + httpRequest.getHeader(HttpRequestUtils.USER_AGENT) + "]");
        }
        return createGeneralPayRequest(httpRequest, msgTag, uaType);
    }

    /**
     * 创建购买礼品业务的支付请求
     *
     * @param httpRequest
     * @return
     */
    public static GeneralPayRequest createGeneralAppPayRequest4BuyVIP(HttpServletRequest httpRequest, ThirdPartyUAType uaType) {
        String msgTag = PayNotifyMsgTag.SCENE_WT_BUY_VIP;
        return createGeneralPayRequest(httpRequest, msgTag, uaType);
    }


    /**
     * 创建购买礼品业务的支付请求
     *
     * @param httpRequest
     * @return
     */
    public static GeneralPayRequest createGeneralAppPayRequestRechargeEnterpriseCard(HttpServletRequest httpRequest, ThirdPartyUAType uaType) {
        String msgTag = com.dfire.soa.market.ent.client.cardcenter.asyncmsg.EntCenterTag.TAG_PAY_ENTERPRISE_CARD_RECHARGE;
        return createGeneralPayRequest(httpRequest, msgTag, uaType);
    }
}
