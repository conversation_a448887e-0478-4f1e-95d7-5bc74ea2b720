/**
 * ==============================================================================
 * Copyright (c) 2016 by www.2dfire.com, All rights reserved.
 * ==============================================================================
 * This software is the confidential and proprietary information of
 * 2dfire.com, Inc. ("Confidential Information"). You shall not disclose
 * such Confidential Information and shall use it only in accordance
 * with the terms of the license agreement you entered into with 2dfire.com, Inc.
 * ------------------------------------------------------------------------------
 * File name:  OAuthHelper.java
 * Author: qiezi
 * Date: 2017/2/25 16:15
 * Description:
 * Nothing.
 * Function List:
 * 1. Nothing.
 * History:
 * 1. Nothing.
 * ==============================================================================
 */
package com.twodfire.wechat.helper;

import com.alibaba.common.convert.Convert;
import com.dfire.consumer.i18n.code.api.WeixinMealCode;
import com.dfire.consumer.util.LoggerUtil;
import com.dfire.soa.oauth.common.constants.ClientSourceEnum;
import com.dfire.soa.oauth.common.constants.Constants;
import com.dfire.soa.oauth.request.biz.OAuthBindMobileRequest;
import com.dfire.soa.oauth.service.IAuthClientService;
import com.dfire.soa.thirdInternal.common.domain.SubscribeMapping;
import com.dfire.soa.thirdInternal.service.ISubscribInternalService;
import com.dfire.soa.turtle.service.ISeatService;
import com.twodfire.share.result.Result;
import com.twodfire.share.util.ResultUtil;
import com.twodfire.wechat.common.local.RequestManager;
import com.twodfire.wechat.common.logger.LoggerMarkers;
import com.twodfire.wechat.common.logger.WXLoggerFactory;
import com.twodfire.wechat.downgrade.DowngradeConfigure;
import com.twodfire.wechat.facade.HppCommonFacade;
import com.twodfire.wechat.props.ActivitiesProperties;
import com.twodfire.wechat.props.RetailShopProperties;
import com.twodfire.wechat.props.WeChatProperties;
import com.twodfire.wechat.thread.AsynTask;
import com.twodfire.wechat.thread.ThreadPoolManager;
import com.twodfire.wechat.utils.CookieUtil;
import com.twodfire.wechat.utils.MobileUtil;
import com.twodfire.wechat.utils.UrlSplitUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * User: qiezi
 * Date: 2017/2/25
 * Time: 16:15
 * Description:
 */
@Component
public class OAuthHelper {

    private ThreadPoolManager threadPoolManager = ThreadPoolManager.INSTANCE;

    @Resource
    private WeChatProperties weChatProperties;

    @Resource
    private ISubscribInternalService subscribInternalService;

    @Resource
    private DowngradeConfigure downgradeConfigure;

    @Resource
    private IAuthClientService authClientService;

    @Resource
    private RetailShopProperties retailShopProperties;

    @Resource
    private ActivitiesProperties activitiesProperties;

    @Resource
    private HppCommonFacade hppCommonFacade;

    @Resource
    private ISeatService seatService;

    @Resource
    private SessionHelper sessionHelper;

    public void resultRedirect(String lang, Result<String> result, HttpServletResponse response) throws Exception {
        if (ResultUtil.isModelNotNull(result)) {
            String url = result.getModel();
            try {
                String cookieName = CookieUtil.getCookieName(result.getModel());
                if (StringUtils.isNotEmpty(cookieName)) {
                    String domain = StringUtils.EMPTY;
                    if (weChatProperties.isDailyModel() || weChatProperties.isDevModel()) {
                        domain = ".whereask.com";
                    } else if (weChatProperties.isPreModel()) {
                        domain = ".2dfire-pre.com";
                    } else if (weChatProperties.isPublishModel()) {
                        domain = ".2dfire.com";
                    }
                    CookieUtil.setCookie("token", cookieName, domain, Constants.CacheKey.EXPIRE_ONE_DAY_TIME_OUT, response);
                }
                url = weChatProperties.multiUrl(url, lang);
            } catch (Exception e) {
                LoggerUtil.warn(WXLoggerFactory.OAUTH_LOGGER, LoggerMarkers.OAUTH_COOKIE_WARN, "set cookie => ", e);
            } finally {
                url = url.replaceAll("[&]+", "&");
                url = url.replaceAll("\\?[&]+", "?");
                Map<String, String> urlMap = UrlSplitUtil.urlSplit(url);
                if (urlMap != null) {
                    if (urlMap.get("anchor") != null) {
                        url += "#/" + urlMap.get("anchor");
                    }
                    //高铁店铺特殊处理
                    if (Convert.asInt(urlMap.get("shop_kind")) == 17 && Convert.asInt(urlMap.get("qr_code")) == 1) {
                        url = StringUtils.replaceAll(url, "qr_code=1", "qr_code=19");
                        LoggerUtil.info(WXLoggerFactory.RAILWAY_LOGGER, LoggerMarkers.RAILWAY_OAUTH, "uid={},entity_id={},profession={},seat_code={}",
                                urlMap.get("uid"), urlMap.get("entity_id"), urlMap.get("profession"), urlMap.get("seat_code"));
                    }
                    //零售店铺分享链接特殊处理
                    if (Convert.asInt(urlMap.get("qr_code")) == 18) {
                        String originalUrl = retailShopProperties.getOriginalShareCallbackUrl();
                        String newUrl = retailShopProperties.getShareCallbackUrl() + "#/homepage";
                        if (url.indexOf(originalUrl) > -1) {
                            url = StringUtils.replaceAll(url, originalUrl, newUrl);
                        }
                    }
                    //营销917活动链接特殊处理 活动类型为11 并且 需要替换entityId
                    if (Convert.asInt(urlMap.get(com.twodfire.wechat.constant.Constants.OAuthBussiness.TYPE)) == 11
                            && Convert.asInt(urlMap.get(com.twodfire.wechat.constant.Constants.OAuthBussiness.NEED_CHANGE_ENTITY_ID)) == 1) {
                        url = changeEntityId(url);
                    }
                }
                response.sendRedirect(url);
                LoggerUtil.info(WXLoggerFactory.BUSINESS_LOGGER, "扫码结果:{}", url);
                return;
            }
        } else {
            if (null != result && StringUtils.isNotEmpty(result.getMessage())) {
                String url = weChatProperties.multiUrl(result.getMessage(), lang);
                response.sendRedirect(url);
                return;
            } else {
                String url = weChatProperties.getErrorGrantUrlByMessage(WeixinMealCode.MULTI_WMI115.getMultiCode(), lang);
                response.sendRedirect(url);
                return;
            }
        }
    }

    public boolean oAuthSecondJudge(String appId, String uid) throws Exception {
        try {
            Result<SubscribeMapping> result = subscribInternalService.findThirdSubscribeMappingByAppIdAndUid(appId, uid);
            if (ResultUtil.isResultSuccess(result) && null == result.getModel()) {
                return true;
            }
            if (ResultUtil.isModelNotNull(result)) {
                SubscribeMapping subscribeMapping = result.getModel();
                sysBindMember(subscribeMapping, uid);
                return false;
            }
        } catch (Exception e) {
            LoggerUtil.error(WXLoggerFactory.OAUTH_LOGGER, LoggerMarkers.THIRD_PLATFORM, e);
        }
        return false;
    }

    private void sysBindMember(final SubscribeMapping subscribeMapping, final String customerRegisterId) {
        if (StringUtils.isEmpty(customerRegisterId)) {
            return;
        }
        threadPoolManager.addCoreExecuteTask(AsynTask.newTask("OAuthHelper_saveThirdAndBindMember_future").registExecute(() -> {
            try {
                if (null != subscribeMapping && StringUtils.isNotEmpty(subscribeMapping.getMobile()) &&
                        downgradeConfigure.isOauthMemberBind()) {
                    OAuthBindMobileRequest oAuthBindMobileRequest = new OAuthBindMobileRequest(MobileUtil.AREA_CODE_CHINA, subscribeMapping.getMobile(), customerRegisterId, subscribeMapping.getAppType(), ClientSourceEnum.H5_CLIENT);
                    Result<String> result = authClientService.bizExecute(oAuthBindMobileRequest);
                    if (!ResultUtil.isModelNotNull(result)) {
                        WXLoggerFactory.OAUTH_LOGGER.warn(LoggerMarkers.THIRD_PLATFORM, "OAuthHelper_saveThirdAndBindMember_future result fail");
                        return null;
                    }
                    //换绑流程，session中的customerRegisterId会变 需要移除
                    HttpServletRequest httpServletRequest = RequestManager.getHttpServletRequest();
                    if (httpServletRequest != null) {
                        httpServletRequest.removeAttribute(com.twodfire.wechat.constant.Constants.Session.SESSION_ID);
                    }
                }
            } catch (Exception ex) {
                WXLoggerFactory.OAUTH_LOGGER.info(LoggerMarkers.THIRD_PLATFORM, "OAuthHelper_saveThirdAndBindMember_future insert fail");
            }
            return null;
        }));
    }

    /**
     * 替换url
     *
     * @param redirectUrl
     * @return
     */
    private String changeEntityId(String redirectUrl) {
        String needChangeEntityId = UrlSplitUtil.urlSplit(redirectUrl).get(com.twodfire.wechat.constant.Constants.OAuthBussiness.NEED_CHANGE_ENTITY_ID);
        String activityEntityId = UrlSplitUtil.urlSplit(redirectUrl).get(com.twodfire.wechat.constant.Constants.OAuthBussiness.ACTIVITY_ENTITY_ID);
        String type = UrlSplitUtil.urlSplit(redirectUrl).get(com.twodfire.wechat.constant.Constants.OAuthBussiness.TYPE);
        String uid = UrlSplitUtil.urlSplit(redirectUrl).get(Constants.OAuth.UID);
        //需要替换entity_id的值为activity_entity_id
        if (StringUtils.isNotBlank(type) && Integer.valueOf(type) == 11 && StringUtils.isNotBlank(needChangeEntityId)
                && needChangeEntityId.equals("1")) {
            redirectUrl = StringUtils.replace(redirectUrl, "entity_id=" + activitiesProperties.getNosActivityShareEntityId(), "entity_id=" + activityEntityId);
        }
        return redirectUrl;
    }

}
