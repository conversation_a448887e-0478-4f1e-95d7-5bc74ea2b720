package com.twodfire.wechat.processor.verifycode;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dfire.consumer.i18n.code.PublicCode;
import com.dfire.consumer.i18n.code.api.WeixinMealCode;
import com.dfire.consumer.util.LoggerUtil;
import com.twodfire.exception.BizException;
import com.twodfire.redis.CodisService;
import com.twodfire.share.result.Result;
import com.twodfire.share.util.ResultUtil;
import com.twodfire.wechat.common.cache.SentinelCacheService;
import com.twodfire.wechat.common.logger.LoggerMarkers;
import com.twodfire.wechat.common.logger.WXLoggerFactory;
import com.twodfire.wechat.constant.Constants;
import com.twodfire.wechat.downgrade.DowngradeConfigure;
import com.twodfire.wechat.enums.VerifyCodeType;
import com.twodfire.wechat.facade.ApiMobileCheckFacade;
import com.twodfire.wechat.param.verifycode.GetVerifyCodeParam;
import com.twodfire.wechat.service.IBindService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/05/22
 */
@Component
public class RoutineVerifyCodeProcess implements IVerifyCodeProcess {
    @Resource
    IBindService bindService;
    @Resource
    private ApiMobileCheckFacade apiMobileCheckFacade;
    @Resource
    private DowngradeConfigure downgradeConfigure;
    @Resource
    private SentinelCacheService cacheService;
    /**
     * 短信验证码模板
     */
    private final static String MESSAGE_CODE = "DS_21478757645";
    @Resource
    private CodisService codisCommonService;

    @Override
    public String type() {
        return VerifyCodeType.ROUTINE.getKey();
    }

    @Override
    public Result<Boolean> getVerifyCode(GetVerifyCodeParam getVerifyCodeParam){
        checkParam(getVerifyCodeParam);
        String mobile = getVerifyCodeParam.getMobile();
        String areaCode = getVerifyCodeParam.getAreaCode();
        String entityId = getVerifyCodeParam.getEntityId();
        String customerId = getVerifyCodeParam.getCustomerId();
        if (StringUtils.isNotBlank(customerId)) {
            //1分钟缓存拦截
            String checkMobile = cacheService.get(getVerifyCodeParam.getCustomerId());
            if (checkMobile != null && checkMobile.equals(mobile)) {
                return ResultUtil.failResult(WeixinMealCode.MULTI_WMI243.getErrCode(), WeixinMealCode.MULTI_WMI243.getMultiCode(), WeixinMealCode.MULTI_WMI243.getMessage());
            }
        }
        Calendar calendar = Calendar.getInstance();
        //1天内同一个手机号发送次数限制
        if (downgradeConfigure.getNoteLimit() != null) {
            String num = codisCommonService.get(com.twodfire.wechat.constant.Constants.CacheKey.NOTE_CHECK_PREFIX + calendar.get(Calendar.DAY_OF_MONTH) + ":" + mobile);
            if (num != null && Integer.valueOf(num) >= downgradeConfigure.getNoteLimit()) {
                return ResultUtil.failResult(WeixinMealCode.MULTI_WMI259.getErrCode(), WeixinMealCode.MULTI_WMI259.getMultiCode(), WeixinMealCode.MULTI_WMI259.getMessage());
            }
        }
        //手机号验证
        if (!apiMobileCheckFacade.isMobileRight(areaCode, mobile)) {
            LoggerUtil.warn(WXLoggerFactory.BUSINESS_LOGGER, LoggerMarkers.MOBILE_VERIFICATION_CODE,
                    "手机号码格式不正确,method:{},area_code:{},mobile:{}", "apiMobileCheckFacade.isMobileRight", areaCode, mobile);
            return ResultUtil.failResult(PublicCode.MULTI_102932.getErrCode(), PublicCode.MULTI_102932.getMultiCode(), PublicCode.MULTI_102932.getMessage());
        }
        Result result = null;
        try {
            result = bindService.sendVerCode(getVerifyCodeParam.getLang(), areaCode, mobile, MESSAGE_CODE, getVerifyCodeParam.getCheckSms(), entityId);
        } catch (Exception e) {

        } finally {
            LoggerUtil.info(WXLoggerFactory.BUSINESS_LOGGER, LoggerMarkers.MOBILE_VERIFICATION_CODE,
                    "手机号验证码发送,method:{},area_code:{},mobile:{},Result:{}", "bindService.sendVerCode", areaCode, mobile, JSONObject.toJSONString(result));
        }
        if (ResultUtil.isResultSuccess(result)) {
            try {
                if (StringUtils.isNotBlank(customerId)) {
                    cacheService.set(customerId, mobile, com.twodfire.wechat.constant.Constants.CacheKey.EXPIRE_TIME_OUT);
                }
                if (downgradeConfigure.getNoteLimit() != null) {
                    codisCommonService.incr(com.twodfire.wechat.constant.Constants.CacheKey.NOTE_CHECK_PREFIX + calendar.get(Calendar.DAY_OF_MONTH) + ":" + mobile,
                            com.twodfire.wechat.constant.Constants.CacheKey.EXPIRE_TIME_1DAYS);
                }
            } catch (Exception e) {

            }
            return ResultUtil.successResult(result.getModel());
        }
        return ResultUtil.failResult(result.getResultCode(), result.getI18nCode(), result.getMessage());
    }

    @Override
    public void checkParam(GetVerifyCodeParam getVerifyCodeParam) {
        if (StringUtils.isBlank(getVerifyCodeParam.getAreaCode()) || StringUtils.isBlank(getVerifyCodeParam.getMobile()) || StringUtils.isBlank(getVerifyCodeParam.getType())) {
            LoggerUtil.warn(WXLoggerFactory.BUSINESS_LOGGER, LoggerMarkers.MOBILE_VERIFICATION_CODE,
                    "getVerifyCode接口参数错误， getVerifyCodeParam：" + JSON.toJSONString(getVerifyCodeParam));
            throw new BizException(PublicCode.MULTI_000072.getMessage(), PublicCode.MULTI_000072.getErrCode(), PublicCode.MULTI_000072.getMultiCode());
        }
        if (Objects.equals(Constants.Default.DEFAULT_ENTITYID, getVerifyCodeParam.getEntityId())) {
            getVerifyCodeParam.setCheckSms(false);
        }
    }
}
