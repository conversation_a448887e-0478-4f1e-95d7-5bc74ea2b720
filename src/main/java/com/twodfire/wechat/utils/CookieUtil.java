package com.twodfire.wechat.utils;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletResponse;

public class CookieUtil {

    public static void setCookie(String name, String value, String domain, int expire, HttpServletResponse response) {
        Cookie cookie = new <PERSON>ie(name, value);
        cookie.setMaxAge(expire);  // 一天
        cookie.setPath("/");//设置cookie存储的虚拟目录
        if (StringUtils.isNotEmpty(domain)) {
            cookie.setDomain(domain);
        }
        response.addCookie(cookie);
    }

    public static String getCookieName(String url) throws Exception {
        String cookieName = StringUtils.EMPTY;
        if (url.contains("token")) {
            String[] pairs = url.split("&");
            if (ArrayUtils.isNotEmpty(pairs)) {
                for (String pair : pairs) {
                    int idx = pair.indexOf("=");
                    if (idx != -1) {
                        String tmp = pair.substring(0, idx);
                        if ("token".equals(tmp)) {
                            cookieName = pair.substring(idx + 1);
                            break;
                        }
                    }
                }
            }
        }
        return cookieName;
    }

}
