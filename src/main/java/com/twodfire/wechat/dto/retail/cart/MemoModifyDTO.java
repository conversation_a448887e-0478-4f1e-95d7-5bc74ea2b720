/*
 * Copyright (C) 2009-2016 Hangzhou 2Dfire Technology Co., Ltd. All rights reserved
 */
package com.twodfire.wechat.dto.retail.cart;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

/**
 * 零售购物车修改备注
 *
 * <AUTHOR>
 * @since 2018-06-05
 */
public class MemoModifyDTO implements Serializable {
    private static final long serialVersionUID = 3376344747320674091L;

    /**
     * 店铺id
     */
    @JsonProperty("entity_id")
    private String entityId;

    /**
     * 备注
     */
    private String memo;

    public String getEntityId() {
        return entityId;
    }

    public void setEntityId(String entityId) {
        this.entityId = entityId;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }
}