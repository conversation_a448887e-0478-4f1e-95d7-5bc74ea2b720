package com.twodfire.wechat.api.pay;

import com.dfire.soa.oauth.common.domain.SessionUserInfo;
import com.dfire.tp.client.bill.response.GetShopSupportPayTypesResponse;
import com.dfire.tp.client.payment.request.PayBillGatheringRequest;
import com.dfire.tp.client.payment.response.PayBillResponse;
import com.twodfire.share.result.Result;
import com.twodfire.share.result.ResultMap;
import com.twodfire.share.util.ApiResultUtil;
import com.twodfire.wechat.api.BaseController;
import com.twodfire.wechat.facade.PayClientFacade;
import com.twodfire.wechat.facade.TradePlatformClientFacade;
import com.twodfire.wechat.facade.dto.TradeParams;
import com.twodfire.wechat.service.impl.support.ThirdPayBillBaseRequest;
import com.twodfire.wechat.utils.HttpRequestUtils;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiOperation;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>  2017-10-30 16:51
 **/
@Api(value = "qrcode_pay(聚合码支付)")
@RestController
@RequestMapping("/qrcode_pay")
public class QRCodePayController extends BaseController {

    @Resource
    private TradePlatformClientFacade tradePlatformClientFacade;

    @Resource
    private PayClientFacade payClientFacade;

    @ApiOperation(value = "检测扫码APP（支付宝/微信）是否是店铺支持的支付类型", response = Result.class)
    @RequestMapping(value = "/v1/check_shop_pay_types", method = RequestMethod.GET)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public ResultMap<GetShopSupportPayTypesResponse> checkShopPayTypes(HttpServletRequest request, @RequestParam(value = "entityId", required = true) String entityId) {
        String requestUA = HttpRequestUtils.getRequestHeaderUserAgent(request);
        //对于H5, supportPayTypes 字段为空 表示不支持，否则表示支持
        GetShopSupportPayTypesResponse response = tradePlatformClientFacade.findShopSupportPayTypes(entityId, requestUA);
        return ApiResultUtil.successResult(response);
    }

    @ApiOperation(value = "立即支付生成支付串", response = Result.class)
    @RequestMapping(value = "/v1/generate_pay_str", method = RequestMethod.POST)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public ResultMap<PayBillResponse> generatePaymentStr(HttpServletRequest request, @RequestBody PayBillGatheringRequest payBillGatheringRequest) {
        SessionUserInfo sessionUserInfo = sessionHelper.getSessionUserInfo(request);
        TradeParams.fillTradeParam(sessionUserInfo,payBillGatheringRequest.getTradeParam());
        return ApiResultUtil.successResult(tradePlatformClientFacade.payQrcode(payBillGatheringRequest));
    }

    @ApiOperation(value = "第三方对接聚合支付生成支付串", response = Result.class)
    @RequestMapping(value = "/v1/third/generate_pay_str", method = RequestMethod.POST)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public ResultMap<PayBillResponse> generateThirdPaymentStr(HttpServletRequest request, @RequestBody ThirdPayBillBaseRequest thirdPayBillBaseRequest) {
        return ApiResultUtil.successResult(payClientFacade.thirdPaymentQrCode(thirdPayBillBaseRequest, request));
    }
}
