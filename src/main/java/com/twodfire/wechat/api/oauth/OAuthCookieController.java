package com.twodfire.wechat.api.oauth;

import com.dfire.consumer.i18n.code.api.WeixinMealCode;
import com.twodfire.share.result.ResultMap;
import com.twodfire.share.util.ApiResultUtil;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * User: edagarli(卤肉)
 * Email: <EMAIL>
 * github: http://github.com/edagarli
 * Date: 16/11/16
 * Time: 16:40
 * Desc: 服务端根域名设置cookie
 */
@RestController
@RequestMapping("/oauth")
public class OAuthCookieController extends BaseOAuthController {

    @RequestMapping(value = "/cookie", method = RequestMethod.GET)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public ResultMap oauthSeatCode(
            @RequestParam(value = "token", required = false) String token,
            HttpServletRequest request,
            HttpServletResponse response) throws Exception {
        Cookie cookie = new Cookie("token", token);
        cookie.setMaxAge(24 * 60 * 60);  // 一天
        cookie.setPath("/");//设置cookie存储的虚拟目录
        if (weChatProperties.isDailyModel() || weChatProperties.isDevModel()) {
            response.addHeader("Access-Control-Allow-Origin", "https://api-l.2dfire-daily.com");
            cookie.setDomain(".whereask.com");
        } else if (weChatProperties.isPreModel()) {
            response.addHeader("Access-Control-Allow-Origin", "https://d.2dfire-pre.com");
            cookie.setDomain(".2dfire-pre.com");
        } else if (weChatProperties.isPublishModel()) {
            response.addHeader("Access-Control-Allow-Origin", "https://d.2dfire.com");
            cookie.setDomain(".2dfire.com");
        }
        response.addCookie(cookie);
        return ApiResultUtil.successResult(WeixinMealCode.MULTI_WMI017.getMultiCode());
    }
}
