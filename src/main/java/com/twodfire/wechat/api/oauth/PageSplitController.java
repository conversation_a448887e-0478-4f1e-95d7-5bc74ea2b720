/**
 * ==============================================================================
 * Copyright (c) 2016 by www.2dfire.com, All rights reserved.
 * ==============================================================================
 * This software is the confidential and proprietary information of
 * 2dfire.com, Inc. ("Confidential Information"). You shall not disclose
 * such Confidential Information and shall use it only in accordance
 * with the terms of the license agreement you entered into with 2dfire.com, Inc.
 * ------------------------------------------------------------------------------
 * File name:  PageSplitController.java
 * Author: qiezi
 * Date: 2016/11/24 11:06
 * Description:
 * Nothing.
 * Function List:
 * 1. Nothing.
 * History:
 * 1. Nothing.
 * ==============================================================================
 */
package com.twodfire.wechat.api.oauth;

import com.alibaba.common.convert.Convert;
import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.dfire.consumer.ap.client.domain.membersystem.MemberSystemVo;
import com.dfire.consumer.i18n.code.PublicCode;
import com.dfire.consumer.util.LoggerUtil;
import com.dfire.consumer.util.annotation.Check;
import com.dfire.consumer.util.annotation.Validator;
import com.dfire.consumer.util.validator.NotBlank;
import com.dfire.soa.consumer.constant.NearbyShopType;
import com.dfire.soa.merchant.bo.ReversiList;
import com.dfire.soa.merchant.enums.ZebraEnum;
import com.dfire.soa.merchant.service.IZebraClientService;
import com.dfire.soa.oauth.common.constants.Constants;
import com.dfire.soa.oauth.common.constants.ThirdPartyUAType;
import com.dfire.soa.shop.bo.Entity;
import com.dfire.soa.shop.constants.ShopConstants;
import com.dfire.soa.shopcenter.bo.ShopMenuTag;
import com.dfire.soa.shopcenter.service.IShopListMenuService;
import com.dfire.soa.thirdInternal.common.constants.ThirdServiceType;
import com.dfire.soa.thirdInternal.common.constants.ThirdVerifyType;
import com.dfire.soa.wechat.service.IWechatService;
import com.dfire.soa.wechat.vo.wechatinfo.WechatInfoVo;
import com.twodfire.exception.BizException;
import com.twodfire.share.result.Result;
import com.twodfire.share.result.ResultMap;
import com.twodfire.share.util.ResultUtil;
import com.twodfire.wechat.api.oauth.handler.PageSplitControllerHandler;
import com.twodfire.wechat.common.logger.LoggerMarkers;
import com.twodfire.wechat.common.logger.WXLoggerFactory;
import com.twodfire.wechat.downgrade.DowngradeConfigure;
import com.twodfire.wechat.enums.OauthTypeEnum;
import com.twodfire.wechat.enums.PageTypeEnum;
import com.twodfire.wechat.facade.ActivityPlatformFacade;
import com.twodfire.wechat.facade.AuthClientFacade;
import com.twodfire.wechat.helper.SessionHelper;
import com.twodfire.wechat.props.CouponCenterProperties;
import com.twodfire.wechat.props.PageSplitUrl;
import com.twodfire.wechat.props.RetailShopProperties;
import com.twodfire.wechat.props.VirtualProperties;
import com.twodfire.wechat.service.IShopService;
import com.twodfire.wechat.utils.HttpRequestUtils;
import com.twodfire.wechat.utils.HttpResponseUtils;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiOperation;
import com.wordnik.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.utils.URLEncodedUtils;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * User: qiezi
 * Date: 2016/11/24
 * Time: 11:06
 * Description:
 */
@Api(value = "页面拆分授权")
@RestController
@RequestMapping("/page_split/v1")
public class PageSplitController extends BaseOAuthController {

    @Resource
    private PageSplitControllerHandler pageSplitControllerHandler;

    @Resource
    private PageSplitUrl pageSplitUrl;

    @Resource
    private AuthClientFacade authClientFacade;

    @Resource
    private ActivityPlatformFacade activityPlatformFacade;

    @Resource
    private DowngradeConfigure downgradeConfigure;

    @Resource
    private IWechatService wechatService;

    @Resource
    private RetailShopProperties retailShopProperties;

    @Resource
    private CouponCenterProperties couponCenterProperties;

    @Resource
    private VirtualProperties virtualProperties;


    @Resource
    private IShopService shopService;

    private static final String INTEGRAL_PARAM = "integral_param";
    private static final String SEAT_CODE = "seat_code";

    @Resource
    private SessionHelper sessionHelper;
    @Resource
    private IZebraClientService zebraClientService;
    @Resource
    private IShopListMenuService shopListMenuService;

    /**
     * 页面拆分v1 不区分业务类型
     *
     * @param router
     * @param entityId
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "按店铺维度页面拆分授权", response = Object.class)
    @RequestMapping(value = "/{router}/{entityId}", method = RequestMethod.GET)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public void oauthPageSplit(@ApiParam(value = "router", required = true)
                               @PathVariable String router,
                               @ApiParam(value = "entityId", required = true)
                               @PathVariable String entityId,
                               @ApiParam(name = "行业，3-零售", required = false)
                               @RequestParam(value = "industry", required = false) Integer industry,
                               @ApiParam(name = "微页面", required = false)
                               @RequestParam(value = "micropage", required = false) String micropage,
                               HttpServletRequest request,
                               HttpServletResponse response) throws Exception {
        //第三方APP接入:世纪联华的内嵌处理
        if (HttpRequestUtils.requestFromThirdAppCenturyMart(request)) {
            String shopWelcomePageUrl = authClientFacade.getShopWelcomeUrl(request, entityId);
            HttpResponseUtils.sendRedirect(shopWelcomePageUrl, null, response);
            return;
        }
        PageTypeEnum pageType = PageTypeEnum.getPageTypeByKey(router);
        if (pageType != null) {
            redirectPage(request, response, pageType.getKey(), StringUtils.EMPTY, StringUtils.EMPTY, entityId, industry, micropage);
        }
        return;
    }

    @ApiOperation(value = "按店铺维度页面拆分授权", response = Object.class)
    @RequestMapping(value = "/integral/{router}/{entityId}/{appId}/{replyId}/{openId}", method = RequestMethod.GET)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public void oauthPageSplitV(@ApiParam(value = "router", required = true)
                                @PathVariable String router,
                                @ApiParam(value = "entityId", required = true)
                                @PathVariable String entityId,
                                @ApiParam(value = "appId", required = true)
                                @PathVariable String appId,
                                @ApiParam(value = "replyId", required = true)
                                @PathVariable String replyId,
                                @ApiParam(value = "openId", required = true)
                                @PathVariable String openId,
                                HttpServletRequest request,
                                HttpServletResponse response) throws Exception {
        PageTypeEnum pageType = PageTypeEnum.getPageTypeByKey(router);
        request.setAttribute(INTEGRAL_PARAM, "appId=" + appId + "&replyId=" + replyId + "&openId=" + openId);
        if (pageType != null) {
            redirectPage(request, response, pageType.getKey(), StringUtils.EMPTY, StringUtils.EMPTY, entityId, null, null);
        }
        return;
    }

    /**
     * 页面拆分v2 区分业务类型（附近的店）
     *
     * @param router
     * @param businessType
     * @param businessId
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "按业务维度页面拆分授权", response = Object.class)
    @RequestMapping(value = "/{router}/{businessType}/{businessId}", method = RequestMethod.GET)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public void oauthTypePageSplit(@ApiParam(value = "router", required = true)
                                   @PathVariable String router,
                                   @ApiParam(value = "businessType", required = true)
                                   @PathVariable String businessType,
                                   @ApiParam(value = "businessId", required = true)
                                   @PathVariable String businessId,
                                   HttpServletRequest request,
                                   HttpServletResponse response) throws Exception {
        PageTypeEnum pageType = PageTypeEnum.getPageTypeByKey(router);
        if (pageType != null) {
            redirectPage(request, response, pageType.getKey(), businessType, StringUtils.EMPTY, businessId, null, null);
        }
        return;
    }


    /**
     * 页面拆分v3 区分子业务类型（附近的店排队和外卖）
     *
     * @param router
     * @param businessType
     * @param businessId
     * @param subBusinessType
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "按业务维度页面拆分授权", response = Object.class)
    @RequestMapping(value = "/{router}/{businessType}/{subBusinessType}/{businessId}", method = RequestMethod.GET)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public void oauthTypePageSplit(@ApiParam(value = "router", required = true)
                                   @PathVariable String router,
                                   @ApiParam(value = "businessType", required = true)
                                   @PathVariable String businessType,
                                   @ApiParam(value = "subBusinessType", required = true)
                                   @PathVariable String subBusinessType,
                                   @ApiParam(value = "businessId", required = true)
                                   @PathVariable String businessId,
                                   HttpServletRequest request,
                                   HttpServletResponse response) throws Exception {
        PageTypeEnum pageType = PageTypeEnum.getPageTypeByKey(router);
        if (pageType != null) {
            Integer industry = null;
            //需要请求行业：1）连锁下的附近店列表
            boolean industryQuery = PageTypeEnum.NEARBY_SHOP.getKey().equals(router)
                    && NearbyShopType.BRAND_ENTITY_ID.getKey().equalsIgnoreCase(businessType) && StringUtils.isNotBlank(businessId);
            if (industryQuery) {
                Entity entity = shopService.getEntityById(businessId);
                if (entity != null) {
                    industry = entity.getIndustry();
                } else {
                    throw new BizException(PublicCode.MULTI_102951.getErrCode());
                }
            }

            redirectPage(request, response, pageType.getKey(), businessType, subBusinessType, businessId, industry, null);
        }
        return;
    }

    private void redirectPage(HttpServletRequest request, HttpServletResponse response, String router,
                              String businessType, String subBusinessType, String businessId, Integer industry, String micropage) throws Exception {
        String entityId = null;
        //业务类型为空则按店铺维度拆分页面 否则按业务来拆分默认店铺id为00000000
        if (StringUtils.isBlank(businessType)
                || NearbyShopType.BRAND_ENTITY_ID.getKey().equalsIgnoreCase(businessType)
                || NearbyShopType.MALL_ENTITY_ID.getKey().equalsIgnoreCase(businessType)
                || PageTypeEnum.RETAIL_PREVIEW.getKey().equals(router)
                || PageTypeEnum.RETAIL_PAGE.getKey().equals(router)
                || PageTypeEnum.SHOP_PROMOTION_LIST.getKey().equals(router)) {
            entityId = businessId;
        }
        if (StringUtils.isNotBlank(businessType) && NearbyShopType.PLATE_ENTITY_ID.getKey().equalsIgnoreCase(businessType)) {
            if (StringUtils.containsIgnoreCase(businessId, ",")) {
                String[] entityIds = StringUtils.split(businessId, ",");
                entityId = entityIds[0];
            } else {
                entityId = businessId;
            }
        }
        String callbackUrl = pageSplitUrl.getPageSplitUrl();
        if (PageTypeEnum.RETAIL_HOME.getKey().equals(router)
                || PageTypeEnum.RETAIL_PREVIEW.getKey().equals(router)
                || PageTypeEnum.RETAIL_PAGE.getKey().equals(router)
                || Convert.asInt(industry) == ShopConstants.INDUSTRY_NEW_RETAIL) {
            callbackUrl = retailShopProperties.getShareCallbackUrl();
        }
        if (PageTypeEnum.COUPON_CENTER_HOME.getKey().equals(router)) {
            callbackUrl = couponCenterProperties.getCouponCenterHomepageUrl();
        }
        if (PageTypeEnum.VIRTUAL.getKey().equals(router)) {
            callbackUrl = virtualProperties.getVirtualDetailUrl();
        }
        StringBuilder builder = new StringBuilder(callbackUrl);
        builder.append("?router=").append(router);
        // 对会员体系会员中心做一次特殊处理
        if (PageTypeEnum.NEW_USER_CENTER.getKey().equals(router) && StringUtils.isNotBlank(businessId)) {
            String userAgent = request.getHeader(Constants.OAuth.UA);
            if (StringUtils.containsIgnoreCase(userAgent, ThirdPartyUAType.WEIXIN_UA.getCode())) {
                entityId = appendWechatAppIdAndReturnEntityId(builder, businessId);
            }
        }
        // Mall小程序版 会员中心页
        if (PageTypeEnum.MALL_MINI_NEW_USER_CENTER.getKey().equals(router)) {
            String userAgent = request.getHeader(Constants.OAuth.UA);
            if (StringUtils.containsIgnoreCase(userAgent, ThirdPartyUAType.WEIXIN_UA.getCode())) {
                appendWechatAppId(builder, request.getQueryString());
            }
        }
        // 门店列表(H5)
        if (PageTypeEnum.H5_SHOP_LIST.getKey().equals(router) && StringUtils.isNotBlank(businessId)) {
            entityId = null;
            Result<ShopMenuTag> result = shopListMenuService.getShowMenuTag(Convert.asLong(businessId));
            if (result.isSuccess() && result.getModel() != null) {
                entityId = result.getModel().getEntityId();
            }
        }
        if (StringUtils.isNotBlank(entityId)) {
            builder.append("&entity_id=").append(entityId);
        }
        if (StringUtils.isNotBlank(businessType)) {
            builder.append("&business_type=").append(businessType);
        }
        if (StringUtils.isNotBlank(subBusinessType)) {
            builder.append("&sub_business_type=").append(subBusinessType);
        }
        if (StringUtils.isNotBlank(businessId)) {
            builder.append("&business_id=").append(businessId);
        }
        if (StringUtils.isNotBlank(micropage)) {
            builder.append("&micropage=").append(micropage);
        }
        if (PageTypeEnum.RETAIL_PREVIEW.getKey().equals(router) && isMicroMall(entityId)) {
            builder.append("&micro_mall=1");
        }
        if (PageTypeEnum.RETAIL_HOME.getKey().equals(router) || Convert.asInt(industry) == ShopConstants.INDUSTRY_NEW_RETAIL) {
            builder.append("&industry=3").append("&theme_jump_flag=1");
        }
        String seatCode = (String) request.getAttribute(SEAT_CODE);
        if (StringUtils.isNotEmpty(seatCode)) {
            builder.append("&seat_cdode=").append(seatCode);
        }
        String integralParam = (String) request.getAttribute(INTEGRAL_PARAM);
        if (!StringUtils.isEmpty(integralParam)) {
            builder.append("&").append(integralParam);
        }
        if (StringUtils.isNotEmpty(request.getQueryString())) {
            builder.append("&").append(request.getQueryString());
        }
        pageSplitControllerHandler.sendRedirect(request, response, builder.toString());
    }

    private String appendWechatAppIdAndReturnEntityId(StringBuilder builder, String businessId) {
        MemberSystemVo memberSystemVo = activityPlatformFacade.getMemberSystemVoByMemberSystemId(businessId);
        if (memberSystemVo == null) {
            return StringUtils.EMPTY;
        }
        if (!downgradeConfigure.isSecondOauthSettingSwitch()) {
            return StringUtils.EMPTY;
        }
        String memberSystemEntityId = memberSystemVo.getMemberSystemBaseInfo().getEntityId();
        String thirdWechatAppId = parseWechatAppIdByEntityId(memberSystemEntityId);
        if (StringUtils.isBlank(thirdWechatAppId)) {
            thirdWechatAppId = memberSystemVo.getMemberSystemBaseInfo().getPublicSign();
        }
        if (StringUtils.isBlank(thirdWechatAppId)) {
            return memberSystemEntityId;
        }
        if (!isWechatVerifyService(thirdWechatAppId)) {
            return memberSystemEntityId;
        }
        builder.append("&").append(Constants.OAuth.THIRD_WECHAT_APP_ID).append("=").append(thirdWechatAppId);
        return memberSystemEntityId;
    }

    /**
     * Mall小程序用
     *
     * @param builder
     * @param queryString http request param String
     */
    private void appendWechatAppId(StringBuilder builder, String queryString) {

        // 解析QueryString
        Map<String, String> paramMap = parseQueryString(queryString);
        // 获取体系id
        String memberSystemId = paramMap.get("member_system_id");
        if (StringUtils.isBlank(memberSystemId)) {
            return;
        }

        MemberSystemVo memberSystemVo = activityPlatformFacade.getMemberSystemVoByMemberSystemId(memberSystemId);
        if (memberSystemVo == null) {
            return;
        }
        if (!downgradeConfigure.isSecondOauthSettingSwitch()) {
            return;
        }
        String memberSystemEntityId = memberSystemVo.getMemberSystemBaseInfo().getEntityId();
        String thirdWechatAppId = parseWechatAppIdByEntityId(memberSystemEntityId);
        if (StringUtils.isBlank(thirdWechatAppId)) {
            thirdWechatAppId = memberSystemVo.getMemberSystemBaseInfo().getPublicSign();
        }
        if (StringUtils.isBlank(thirdWechatAppId)) {
            return;
        }
        if (!isWechatVerifyService(thirdWechatAppId)) {
            return;
        }
        builder.append("&").append(Constants.OAuth.THIRD_WECHAT_APP_ID).append("=").append(thirdWechatAppId);
    }

    private Map<String, String> parseQueryString(String queryString) {

        List<NameValuePair> nameValuePairs = URLEncodedUtils.parse(queryString, Charset.forName("UTF-8"));
        if (CollectionUtils.isEmpty(nameValuePairs)) {
            return new HashMap<>();
        }

        return nameValuePairs.stream().collect(Collectors.toMap(NameValuePair::getName, NameValuePair::getValue));
    }

    private boolean isWechatVerifyService(String thirdWechatAppId) {
        Result<WechatInfoVo> result = wechatService.getWechatInfoByAppId(thirdWechatAppId);
        if (ResultUtil.isModelNotNull(result)) {
            WechatInfoVo wechatInfoVo = result.getModel();
            return wechatInfoVo.getServiceTypeInfo() == ThirdServiceType.THIRD_SERVICE.getValue() && wechatInfoVo.getVerifyTypeInfo() == ThirdVerifyType.THIRD_CERTIFIED.getValue();
        }
        return false;
    }

    private String parseWechatAppIdByEntityId(String entityId) {
        try {
            String secondOauthSetting = downgradeConfigure.getSecondOauthSetting();
            Map<String, Object> oauthSettingMap = JSON.parseObject(secondOauthSetting).getInnerMap();
            return oauthSettingMap.getOrDefault(entityId, StringUtils.EMPTY).toString();
        } catch (Exception e) {
            return StringUtils.EMPTY;
        }
    }

    /**
     * 单页授权统一入口
     *
     * @param oauthUrl 授权url
     * @return Result
     * @throws Exception
     */
    @ApiOperation(value = "单页授权", response = ResultMap.class)
    @RequestMapping(value = "/oauth_page_split", method = RequestMethod.GET)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    @Validator({@Check(name = "oauth_url", adapter = NotBlank.class, message = "授权url不能为空", errorCode = "0")})
    public void oauthPageSplit(
            @ApiParam(value = "单页授权url", required = true)
            @RequestParam(value = "oauth_url", required = true) String oauthUrl,
            @ApiParam(value = "授权业务类型", required = true)
            @RequestParam(value = "oauth_type", required = true) String oauthType,
            @ApiParam(value = "授权店铺ID", required = false)
            @RequestParam(value = "oauth_entity_id", required = false) String oauthEntityId,
            @ApiParam(value = "授权处理器类型", required = false)
            @RequestParam(value = "oauth_handler_type", required = false) String oauthHandlerType,
            HttpServletRequest request,
            HttpServletResponse response
    ) throws Exception {
        StringBuilder builder = new StringBuilder();
        builder.append(oauthUrl);
        //添加页面锚点
        OauthTypeEnum oauthTypeEnum = OauthTypeEnum.typeOf(oauthType);
        if (oauthTypeEnum != null) {
            builder.append(oauthTypeEnum.getAnchor());
        }
        //添加业务参数
        if (StringUtils.isNotEmpty(request.getQueryString())) {
            builder.append("?").append(request.getQueryString());
        }
        //去除非业务参数
        String responseUrl = builder.toString();
        responseUrl = replaceValueReg(responseUrl, "oauth_url", "oauth_entity_id", "oauth_type"
                , "oauth_handler_type", "t", "name", "g_entityId", "xtoken");
        LoggerUtil.info(WXLoggerFactory.PAGE_SPLIT_LOGGER, LoggerMarkers.PAGE_SPLIT
                , "oauthType={}, oauthUrl={},oauthEntityId={},oauth_handler_type={},responseUrl={}"
                , oauthType, oauthUrl, oauthEntityId, oauthHandlerType, responseUrl);
        pageSplitControllerHandler.sendRedirect(request, response, responseUrl);
        return;
    }

    /**
     * 页面拆分地址
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "页面拆分地址", response = Object.class)
    @RequestMapping(value = "/get_page_split_url/{entityId}", method = RequestMethod.GET)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public Map<String, String> getPageSplitUrl(@ApiParam(value = "店铺ID", required = true)
                                               @PathVariable String entityId) throws Exception {
        Map urlMap = new HashMap();
        urlMap.put(PageTypeEnum.MENU.getKey(), produceUrl(entityId, PageTypeEnum.MENU.getKey()));
        urlMap.put(PageTypeEnum.TAKEOUT_MENU.getKey(), produceUrl(entityId, PageTypeEnum.TAKEOUT_MENU.getKey()));
        urlMap.put(PageTypeEnum.SHOP_ORDER.getKey(), produceUrl(entityId, PageTypeEnum.SHOP_ORDER.getKey()));
        urlMap.put(PageTypeEnum.ORDER_DISH.getKey(), produceUrl(entityId, PageTypeEnum.ORDER_DISH.getKey()));
        urlMap.put(PageTypeEnum.ALL_CARD.getKey(), produceUrl("00000000", PageTypeEnum.ALL_CARD.getKey()));
        urlMap.put(PageTypeEnum.SHOP_CARD.getKey(), produceUrl(entityId, PageTypeEnum.SHOP_CARD.getKey()));
        urlMap.put(PageTypeEnum.ALL_COUPON.getKey(), produceUrl("00000000", PageTypeEnum.ALL_COUPON.getKey()));
        urlMap.put(PageTypeEnum.SHOP_COUPON.getKey(), produceUrl(entityId, PageTypeEnum.SHOP_COUPON.getKey()));
        urlMap.put(PageTypeEnum.BILL.getKey(), produceUrl(entityId, PageTypeEnum.BILL.getKey()));
        urlMap.put(PageTypeEnum.SHOP.getKey(), produceUrl(entityId, PageTypeEnum.SHOP.getKey()));
        urlMap.put(PageTypeEnum.ORDER_LIST.getKey(), produceUrl(entityId, PageTypeEnum.ORDER_LIST.getKey()));
        urlMap.put(PageTypeEnum.USER_INFO.getKey(), produceUrl(entityId, PageTypeEnum.USER_INFO.getKey()));
        urlMap.put(PageTypeEnum.USER_CENTER.getKey(), produceUrl(entityId, PageTypeEnum.USER_CENTER.getKey()));
        urlMap.put(PageTypeEnum.TAKE_OUT_ADDR.getKey(), produceUrl(entityId, PageTypeEnum.TAKE_OUT_ADDR.getKey()));
        urlMap.put(PageTypeEnum.MEMBER_SYSTEM_STORE_PUSH.getKey(), produceUrl(entityId, PageTypeEnum.MEMBER_SYSTEM_STORE_PUSH.getKey()));
        return urlMap;
    }

    private String produceUrl(String entityId, String router) {
        String url = pageSplitUrl.getPageSplitPrefix() + router + "/" + entityId;
        return url;
    }

    /**
     * url替换参数
     *
     * @param url    the url
     * @param params the params
     * @return string string
     */
    private static String replaceValueReg(String url, String... params) {
        for (String param : params) {
            if (StringUtils.isNotBlank(url)) {
                url = url.replaceAll("(\\?" + param + "=[^&]*)", "?");
                url = url.replaceAll("(&" + param + "=[^&]*)", "&");
            }
        }
        return url;
    }

    /**
     * 判断店铺是否是餐饮微商城
     *
     * @param entityId
     * @return
     */
    public Boolean isMicroMall(String entityId) {
        Entity entity = shopService.getEntityById(entityId);
        if (null == entity) {
            return false;
        }
        if (ShopConstants.INDUSTRY_RESTAURANT != entity.getIndustry()) {
            return false;
        }
        Result<ReversiList> reversiListResult = zebraClientService.selectByTypeAndZebra(ZebraEnum.MICRO_HYPERMARKET.getType(), entityId);
        ReversiList model = reversiListResult.getModel();
        return null != model && model.getIsValid();
    }

}
