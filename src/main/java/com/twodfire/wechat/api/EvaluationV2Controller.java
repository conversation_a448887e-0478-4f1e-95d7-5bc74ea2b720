package com.twodfire.wechat.api;

import com.alibaba.fastjson.JSON;
import com.dfire.consumer.ap.client.center.vo.activity.EvaluateGiftDetailVo;
import com.dfire.consumer.ap.client.service.gift.IEvaluateGiftService;
import com.dfire.consumer.i18n.code.PublicCode;
import com.dfire.consumer.util.LoggerUtil;
import com.dfire.koubei.llm.service.ILlmService;
import com.dfire.soa.item.bo.ItemMinimalistBO;
import com.dfire.soa.item.platform.service.read.IItemReadForBossService;
import com.dfire.soa.mmt.ac.client.dto.ActivityCenterCouponDto;
import com.dfire.soa.mmt.ac.client.dto.ActivityCenterPreferentialCouponDto;
import com.dfire.soa.mmt.ac.client.dto.ActivityCenterPreferentialPointsDto;
import com.dfire.soa.oauth.common.domain.SessionUserInfo;
import com.dfire.soa.shop.bo.Shop;
import com.dfire.soa.waiter.facade.ICommentSettingFacade;
import com.dfire.soa.waiter.facade.IIndividualCommentFacade;
import com.dfire.soa.waiter.model.UserCommentCount;
import com.dfire.soa.waiter.vo.*;
import com.dfire.soa.waiter.vo.request.ConditionCountParam;
import com.dfire.soa.waiter.vo.request.QueryCommentParam;
import com.dfire.soa.waiter.vo.request.ScoreLevelCountParam;
import com.twodfire.share.result.Result;
import com.twodfire.share.result.ResultMap;
import com.twodfire.share.util.ApiResultUtil;
import com.twodfire.share.util.ResultUtil;
import com.twodfire.wechat.common.logger.LoggerMarkers;
import com.twodfire.wechat.common.logger.WXLoggerFactory;
import com.twodfire.wechat.facade.ShopClientFacade;
import com.twodfire.wechat.param.torder.HiddenOptParam;
import com.twodfire.wechat.vo.evaluation.CommentConfigVO;
import com.twodfire.wechat.vo.evaluation.CommentReviewRewardReqVO;
import com.twodfire.wechat.vo.evaluation.CommentSaveReqVO;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;


@Api(value = "evaluation(服务评价&绩效)")
@RestController
@RequestMapping(value = "/evaluation/v2")
public class EvaluationV2Controller extends BaseController {

    @Resource
    IIndividualCommentFacade individualCommentFacade;
    @Resource
    ICommentSettingFacade commentSettingFacade;
    @Resource
    ILlmService llmService;
    @Resource
    IEvaluateGiftService evaluateGiftService;
    @Resource
    IItemReadForBossService itemReadForBossService;
    @Resource
    ShopClientFacade shopClientFacade;

    @ApiOperation(value = "conditionCount 统计", response = ResultMap.class)
    @RequestMapping(value = "condition_count", method = RequestMethod.GET)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public Object conditionCount(HttpServletRequest request,
                                 @RequestParam(value = "entity_id", required = true) String entityId,
                                 @RequestParam(value = "worker_id", required = true) String workerId) throws Exception {
        ConditionCountParam conditionCountParam = new ConditionCountParam();
        conditionCountParam.setEntityIds(Collections.singletonList(entityId));
        conditionCountParam.setIndividualCustomerRegisterIds(Collections.singletonList(workerId));
        conditionCountParam.setContentTypes(Collections.singletonList(2));
        Result<UserCommentCount> data = individualCommentFacade.conditionCount(conditionCountParam);
        if (!ResultUtil.isResultSuccess(data)) {
            return ApiResultUtil.failResult(data.getResultCode(), data.getMessage());
        }
        return ApiResultUtil.successResult(data.getModel());
    }

    @ApiOperation(value = "scoreLevelCount 统计", response = ResultMap.class)
    @RequestMapping(value = "score_level_count", method = RequestMethod.GET)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public Object scoreLevelCount(HttpServletRequest request,
                                  @RequestParam(value = "entity_id", required = true) String entityId,
                                  @RequestParam(value = "worker_id", required = true) String workerId) throws Exception {
        ScoreLevelCountParam scoreLevelCountParam = new ScoreLevelCountParam();
        scoreLevelCountParam.setEntityIds(Collections.singletonList(entityId));
        scoreLevelCountParam.setIndividualCustomerRegisterIds(Collections.singletonList(workerId));
        Result<UserCommentCount> data = individualCommentFacade.scoreLevelCount(scoreLevelCountParam);
        if (!ResultUtil.isResultSuccess(data)) {
            return ApiResultUtil.failResult(data.getResultCode(), data.getMessage());
        }
        return ApiResultUtil.successResult(data.getModel());
    }

    @ApiOperation(value = "saveIndividualComment 保存评分", response = ResultMap.class)
    @RequestMapping(value = "comments", method = RequestMethod.POST)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public Object saveIndividualComment(HttpServletRequest request,
                                        @RequestBody MenuCommentVO param) throws Exception {
        LoggerUtil.info(WXLoggerFactory.BUSINESS_LOGGER, LoggerMarkers.BUSINESS,
                "saveIndividualComment param :{}", JSON.toJSONString(param));
        SessionUserInfo sessionUserInfo = sessionHelper.getSessionUserInfo(request);
        param.setFromUserId(sessionUserInfo.getCustomerId());
        Result<Long> data = individualCommentFacade.saveComment(param);
        if (!ResultUtil.isResultSuccess(data)) {
            return ApiResultUtil.failResult(data.getResultCode(), data.getMessage());
        }
        return ApiResultUtil.successResult(String.valueOf(data.getModel()));
    }

    @ApiOperation(value = "commentSuggestions 评论建议", response = ResultMap.class)
    @RequestMapping(value = "comment_suggestions", method = RequestMethod.GET)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public Object commentSuggestions(HttpServletRequest request,
                                     @RequestParam(value = "entity_id", required = true) String entityId
    ) throws Exception {
        LoggerUtil.info(WXLoggerFactory.BUSINESS_LOGGER, LoggerMarkers.BUSINESS,
                "commentSuggestions param :{}", JSON.toJSONString(entityId));
        SessionUserInfo sessionUserInfo = sessionHelper.getSessionUserInfo(request);
        Result<AiCommentCommand> aiCommandResult = commentSettingFacade.getAiCommentCommand(entityId);
        if (!ResultUtil.isModelNotNull(aiCommandResult)) {
            return ApiResultUtil.failResult(aiCommandResult.getResultCode(), aiCommandResult.getMessage());
        }
        String desc = "%s是一个%s店铺，所在地区使用的语言是%s,这个店铺的特色菜是%s。%s";
        Shop shop = shopClientFacade.queryShopDetail(entityId);
        String lang = sessionUserInfo.getLang();
        String shopName = shop.getName();
        String industry = shop.getBusinessType() == 2 ? "公司" : "餐饮";
        String baseDescription = aiCommandResult.getModel().getAiEvaluationCommand();
        String recommendedItems = aiCommandResult.getModel().getEvaluationRecommendedItems();
        if (StringUtils.isBlank(recommendedItems)) {
            Result<List<ItemMinimalistBO>> recommendItemsResult = itemReadForBossService.getRecommendItems(entityId);
            if (ResultUtil.isModelNotNull(recommendItemsResult)) {
                recommendedItems = recommendItemsResult.getModel().stream().limit(2).map(ItemMinimalistBO::getName).collect(Collectors.joining("、"));
            }
        }
        desc = String.format(desc, shopName, industry, lang, recommendedItems, baseDescription);
        Result<String> result = llmService.generateDetailedComment(desc);
        if (!ResultUtil.isModelNotNull(result)) {
            return ApiResultUtil.failResult(result.getResultCode(), result.getMessage());
        }
        return ApiResultUtil.successResult(result.getModel());
    }

    @ApiOperation(value = "commentConfig 评论配置", response = ResultMap.class)
    @RequestMapping(value = "comment_config", method = RequestMethod.GET)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public Object commentConfig(HttpServletRequest request,
                                @RequestParam(value = "entity_id", required = true) String entityId,
                                @RequestParam(value = "target_id", required = false) String target_id,
                                @RequestParam(value = "version", required = false) String version
    ) throws Exception {
        LoggerUtil.info(WXLoggerFactory.BUSINESS_LOGGER, LoggerMarkers.BUSINESS,
                "commentSuggestions param :{}", JSON.toJSONString(entityId));
        SessionUserInfo sessionUserInfo = sessionHelper.getSessionUserInfo(request);
        CommentConfigVO configVO = new CommentConfigVO();
        configVO.setCanJoin(false);
        String commentTips = "MULTI_CSS888" + "%s" + "MULTI_CSS889" + "MULTI_CSS225" + "%s！";
        String rewardTips = "%s；" + "MULTI_CSS888" + "MULTI_CSS890" + "MULTI_CSS889，" + "MULTI_CSS225" + "%s！";
        String platformDesc = "";
        List<String> rewardDesc = new ArrayList<>();
        Result<List<CommentPlatformVO>> platformResult;
        if (StringUtils.isNotEmpty(version)) {
            platformResult = commentSettingFacade.getCommentPlatformListV2(entityId, true);
        }else {
            platformResult = commentSettingFacade.getCommentPlatformList(entityId, true);
        }
        if (ResultUtil.isModelNotNull(platformResult)) {
            configVO.setPlatforms(platformResult.getModel());
            platformDesc = platformResult.getModel().stream().map(CommentPlatformVO::getPlatformName).collect(Collectors.joining("、"));
        }
        Result<EvaluateGiftDetailVo> activityResult = evaluateGiftService.getReleasedActivityDetail(entityId);
        if (ResultUtil.isModelNotNull(activityResult)) {
            Result<Boolean> checkResult = evaluateGiftService.checkUserIsApply(sessionUserInfo.getCustomerId(), activityResult.getModel().getActivityEntityId());
            if (ResultUtil.isModelNotNull(checkResult)) {
                configVO.setCanJoin(checkResult.getModel());
            }
            Optional.ofNullable(activityResult.getModel().getRuleDtos()).map(rules -> rules.get(0)).ifPresent(rule -> {
                Optional.ofNullable(rule.getCoupon()).map(ActivityCenterPreferentialCouponDto::getCoupons).ifPresent(coupons -> {
                    Integer couponNum = coupons.stream().map(ActivityCenterCouponDto::getNumber).mapToInt(Integer::intValue).sum();
                    rewardDesc.add(couponNum + "MULTI_CSS100" + "MULTI_CSS221");
                });
                Optional.ofNullable(rule.getPoints()).map(ActivityCenterPreferentialPointsDto::getPoints).ifPresent(points -> {
                    rewardDesc.add(points + "MULTI_CSS384");
                });
            });
        }
        if (StringUtils.isNotBlank(platformDesc) && !CollectionUtils.isEmpty(rewardDesc)) {
            configVO.setCommentTipsDesc(String.format(commentTips, platformDesc, String.join("、", rewardDesc)));
            configVO.setRewardTipsDesc(String.format(rewardTips, platformDesc, String.join("、", rewardDesc)));
        }
        configVO.setRewardTipsPicUrls(Collections.singletonList("https://assets.2dfire.com/frontend/48fc35c1abae6c368c0d09fe84da1899.png"));
        if (StringUtils.isNotBlank(target_id)) {
            QueryCommentParam queryCommentParam = new QueryCommentParam();
            queryCommentParam.setOrderType(2);
            queryCommentParam.setPage(1);
            queryCommentParam.setPageSize(1);
            queryCommentParam.setQueryMySend(true);
            queryCommentParam.setCurrentClientCustomerRegisterId(sessionUserInfo.getCustomerId());
            queryCommentParam.setIndividualCustomerRegisterIds(Collections.singletonList(target_id));
            queryCommentParam.setEntityIds(Collections.singletonList(entityId));
            queryCommentParam.setCommentTypeList(Collections.singletonList(0));
            queryCommentParam.setContentType(0);
            Result<QueryCommentListVO> commentVOResult = individualCommentFacade.queryCommentList(queryCommentParam);
            if (ResultUtil.isModelNotNull(commentVOResult) && CollectionUtils.isEmpty(commentVOResult.getModel().getCommentList())) {
                MenuCommentVO menuCommentVO = commentVOResult.getModel().getCommentList().get(0);
                configVO.setLastCommentId(menuCommentVO.getStrId());
                configVO.setLastCommentDate(menuCommentVO.getTimezoneDate());
            }
        }
        return ApiResultUtil.successResult(configVO);
    }


    @ApiOperation(value = "saveCommentContent 保存评论内容", response = ResultMap.class)
    @RequestMapping(value = "save_comment_content", method = RequestMethod.POST)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public Object saveIndividualCommentContent(HttpServletRequest request,
                                               @RequestBody CommentSaveReqVO req) throws Exception {
        LoggerUtil.info(WXLoggerFactory.BUSINESS_LOGGER, LoggerMarkers.BUSINESS,
                "saveCommentContent param :{}", JSON.toJSONString(req));
        MenuCommentVO param = new MenuCommentVO();
        param.setStrId(req.getCommentId());
        param.setEntityId(req.getEntityId());
        param.setCommentTargetId(req.getTargetId());
        Optional.ofNullable(req.getOrderId()).ifPresent(param::setOrderId);
        Optional.ofNullable(req.getReplyId()).ifPresent(param::setReplyIdStr);
        Optional.ofNullable(req.getContent()).ifPresent(param::setComment);
        Optional.ofNullable(req.getPicUrls()).ifPresent(param::setPicUrlList);
        SessionUserInfo sessionUserInfo = sessionHelper.getSessionUserInfo(request);
        param.setFromUserId(sessionUserInfo.getCustomerId());
        Result<Long> data = individualCommentFacade.saveCommentContent(param);
        if (!ResultUtil.isResultSuccess(data)) {
            return ApiResultUtil.failResult(data.getResultCode(), data.getMessage());
        }
        return ApiResultUtil.successResult(String.valueOf(data.getModel()));
    }

    @ApiOperation(value = "saveCommentReviewReward 保存评论有礼", response = ResultMap.class)
    @RequestMapping(value = "comment_review_reward", method = RequestMethod.POST)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public Object saveIndividualCommentReviewReward(HttpServletRequest request,
                                                    @RequestBody CommentReviewRewardReqVO req) throws Exception {
        LoggerUtil.info(WXLoggerFactory.BUSINESS_LOGGER, LoggerMarkers.BUSINESS,
                "saveCommentReviewReward param :{}", JSON.toJSONString(req));
//        CommentReviewRewardVO respVO = new CommentReviewRewardVO();
//        respVO.setErrorStatus(0);
        MenuCommentVO param = new MenuCommentVO();
        MenuCommentReviewRewardVO rewardParam = new MenuCommentReviewRewardVO();
        param.setReviewRewardVO(rewardParam);
        param.setEntityId(req.getEntityId());
        Optional.ofNullable(req.getCommentId()).ifPresent(param::setStrId);
        Optional.ofNullable(req.getTargetId()).ifPresent(param::setCommentTargetId);
        SessionUserInfo sessionUserInfo = sessionHelper.getSessionUserInfo(request);
        param.setFromUserId(sessionUserInfo.getCustomerId());
        rewardParam.setScreenshotUrl(req.getScreenshotUrl());
        rewardParam.setUploadTime(System.currentTimeMillis());
        Result auditSaveResult = individualCommentFacade.commentReviewReward(param);
        if (!ResultUtil.isResultSuccess(auditSaveResult)) {
            return ApiResultUtil.failResult(auditSaveResult.getResultCode(), auditSaveResult.getMessage());
        }
        return ApiResultUtil.defaultResult();
    }

    @ApiOperation(value = "hiddenOpt 隐藏操作", response = ResultMap.class)
    @RequestMapping(value = "hidden_opt", method = RequestMethod.POST)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public Object hiddenOpt(HttpServletRequest request,
                            @RequestBody HiddenOptParam hiddenOptParam
    ) throws Exception {
        LoggerUtil.info(WXLoggerFactory.BUSINESS_LOGGER, LoggerMarkers.BUSINESS,
                "hiddenOpt param :{}", JSON.toJSONString(hiddenOptParam));
        SessionUserInfo sessionUserInfo = sessionHelper.getSessionUserInfo(request);
        Result data = individualCommentFacade.hiddenOpt(Long.valueOf(hiddenOptParam.getStrId()),
                hiddenOptParam.getOptType(), sessionUserInfo.getCustomerId());
        if (!ResultUtil.isResultSuccess(data)) {
            return ApiResultUtil.failResult(data.getResultCode(), data.getMessage());
        }
        return ApiResultUtil.successResult(Boolean.TRUE);
    }

    @ApiOperation(value = "goodEvaluateRate 好评比例", response = ResultMap.class)
    @RequestMapping(value = "good_evaluate_rate", method = RequestMethod.GET)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public Object goodEvaluateRate(HttpServletRequest request
            , @RequestParam(value = "comment_id", required = true) String commentId,
                                   @RequestParam(value = "entity_id", required = true) String entityId
    ) throws Exception {
        LoggerUtil.info(WXLoggerFactory.BUSINESS_LOGGER, LoggerMarkers.BUSINESS,
                "goodEvaluateRate param :{}", JSON.toJSONString(commentId));
        SessionUserInfo sessionUserInfo = sessionHelper.getSessionUserInfo(request);
        Result<GoodEvaluateRateVO> data = individualCommentFacade.goodEvaluateRate(entityId, sessionUserInfo.getCustomerId());
        if (!ResultUtil.isResultSuccess(data)) {
            return ApiResultUtil.failResult(data.getResultCode(), data.getMessage());
        }
        return ApiResultUtil.successResult(data.getModel());
    }


    @ApiOperation(value = "queryMobileByCommentId 查询手机号", response = ResultMap.class)
    @RequestMapping(value = "query_mobile_by_comment_id", method = RequestMethod.GET)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public Object queryMobileByCommentId(HttpServletRequest request
            , @RequestParam(value = "comment_id", required = true) String commentId,
                                         @RequestParam(value = "entity_id", required = true) String entityId
    ) throws Exception {
        LoggerUtil.info(WXLoggerFactory.BUSINESS_LOGGER, LoggerMarkers.BUSINESS,
                "queryMobileByCommentId param :{}", JSON.toJSONString(commentId));
        SessionUserInfo sessionUserInfo = sessionHelper.getSessionUserInfo(request);
        Result<QueryMobileByCommentIdVO> data = individualCommentFacade.queryMobileByCommentId(entityId, commentId, sessionUserInfo.getCustomerId());
        if (!ResultUtil.isResultSuccess(data)) {
            return ApiResultUtil.failResult(data.getResultCode(), data.getMessage());
        }
        return ApiResultUtil.successResult(data.getModel());
    }

    @ApiOperation(value = "queryPermissions查询权限列表", response = ResultMap.class)
    @RequestMapping(value = "query_permissions", method = RequestMethod.GET)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public Object queryPermissions(HttpServletRequest request
            , @RequestParam(value = "entity_id", required = true) String entityId
    ) throws Exception {
        LoggerUtil.info(WXLoggerFactory.BUSINESS_LOGGER, LoggerMarkers.BUSINESS,
                "queryPermissions param :{}", JSON.toJSONString(entityId));
        SessionUserInfo sessionUserInfo = sessionHelper.getSessionUserInfo(request);
        Result<List<String>> data = individualCommentFacade.queryPermissions(entityId, sessionUserInfo.getCustomerId());
        if (!ResultUtil.isResultSuccess(data)) {
            return ApiResultUtil.failResult(data.getResultCode(), data.getMessage());
        }
        return ApiResultUtil.successResult(data.getModel());
    }


    @ApiOperation(value = "查询个人主页相关的评论列表", response = ResultMap.class)
    @RequestMapping(value = "query_comment_list", method = RequestMethod.GET)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public Object queryCommentListByPage(HttpServletRequest request,
                                         @RequestParam(value = "return_hidden_data", required = false, defaultValue = "1") Integer returnHiddenData,
                                         @RequestParam(value = "sort_by", required = false) Integer orderType,
                                         @RequestParam(value = "score_levels", required = false) List<Integer> scoreLevels,
                                         @RequestParam(value = "end_time", required = false) Long endTime,
                                         @RequestParam(value = "start_time", required = false) Long startTime,
                                         @RequestParam(value = "user_id_list", required = false) List<String> userIdList,
                                         @RequestParam(value = "entity_id_list", required = false) List<String> entityIdList,
                                         @RequestParam(value = "comment_id", required = false) String commentId,
                                         @RequestParam(value = "order_id", required = false) String orderId,
                                         @RequestParam(value = "inner_code", required = false) String innerCode,
                                         @RequestParam(value = "query_my_send", required = false) Boolean queryMySend,
                                         @RequestParam(value = "content_type", required = false) Integer contentType,
                                         @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                                         @RequestParam(value = "page_size", required = false, defaultValue = "10") Integer pageSize) throws Exception {
        SessionUserInfo sessionUserInfo = sessionHelper.getSessionUserInfo(request);
        QueryCommentParam queryCommentParam = new QueryCommentParam();
        queryCommentParam.setReturnHiddenData(1 == returnHiddenData);
        queryCommentParam.setOrderType(orderType);
        queryCommentParam.setPage(page);
        queryCommentParam.setPageSize(pageSize);
        queryCommentParam.setScoreLevels(scoreLevels);
        queryCommentParam.setEndTime(endTime);
        queryCommentParam.setStartTime(startTime);
        queryCommentParam.setQueryMySend(queryMySend);
        queryCommentParam.setCurrentClientCustomerRegisterId(sessionUserInfo.getCustomerId());
        queryCommentParam.setIndividualCustomerRegisterIds(userIdList);
        queryCommentParam.setEntityIds(entityIdList);

        // 内部码和订单号
//        queryCommentParam.setInnerCode(innerCode);
        queryCommentParam.setOrderId(orderId);
        queryCommentParam.setContentType(contentType);
        if (StringUtils.isNotBlank(commentId)) {
            queryCommentParam.setIds(Collections.singletonList(Long.valueOf(commentId)));
        }
        Result<QueryCommentListVO> queryCommentListVOResult = individualCommentFacade.queryCommentList(queryCommentParam);
        if (!queryCommentListVOResult.isSuccess()) {
            return ApiResultUtil.failResult(PublicCode.MULTI_101550.getErrCode(), PublicCode.MULTI_101550.getMessage());
        }
        queryCommentListVOResult.getModel().getCommentList().forEach(comment -> {
            if (Objects.equals(1, comment.getCommentOption())) {
                Result<EvaluateGiftDetailVo> activityResult = evaluateGiftService.getReleasedActivityDetail(comment.getEntityId());
                if (ResultUtil.isModelNotNull(activityResult)) {
                    Result<Boolean> checkResult = evaluateGiftService.checkUserIsApply(sessionUserInfo.getCustomerId(), activityResult.getModel().getActivityId());
                    if (ResultUtil.isModelNotNull(checkResult) && checkResult.getModel()) {
                        comment.setCommentOption(3);
                    }
                }
            }
        });
        return ApiResultUtil.successResult(queryCommentListVOResult.getModel());
    }

    //systemConfig
    @ApiOperation(value = "systemConfig", response = ResultMap.class)
    @RequestMapping(value = "system_config", method = RequestMethod.GET)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public Object systemConfig(HttpServletRequest request, @RequestParam(value = "entity_id", required = true) String entityId) throws Exception {
        LoggerUtil.info(WXLoggerFactory.BUSINESS_LOGGER, LoggerMarkers.BUSINESS,
                "systemConfig param :{}", JSON.toJSONString(entityId));
        Result<com.dfire.soa.waiter.vo.CommentConfigVO > data = individualCommentFacade.systemConfig(entityId);
        if (!ResultUtil.isResultSuccess(data)) {
            return ApiResultUtil.failResult(data.getResultCode(), data.getMessage());
        }
        return ApiResultUtil.successResult(data.getModel());

    }


}


