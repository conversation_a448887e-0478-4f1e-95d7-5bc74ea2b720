package com.twodfire.wechat.api;

import com.alibaba.common.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dfire.consumer.i18n.code.PublicCode;
import com.dfire.consumer.i18n.code.api.WeixinMealCode;
import com.dfire.consumer.i18n.constant.LangConstant;
import com.dfire.consumer.util.LoggerUtil;
import com.dfire.soa.consumer.bo.GooderTransEarlier;
import com.dfire.soa.consumer.bo.GoodsTransLater;
import com.dfire.soa.consumer.cart.cartcenter.cloudcart.request.CloudCartJoinTableRequest;
import com.dfire.soa.consumer.cart.cartcenter.cloudcart.response.CloudCartJoinTableResponse;
import com.dfire.soa.consumer.param.CartItem;
import com.dfire.soa.consumer.service.cart.ICartCenterService;
import com.dfire.soa.consumer.service.order.IOrderService;
import com.dfire.soa.oauth.common.constants.ClientSourceEnum;
import com.dfire.soa.oauth.common.domain.SessionUserInfo;
import com.dfire.soa.order.bo.WaitingOrder;
import com.dfire.soa.order.constant.ClientFromConstant;
import com.dfire.soa.order.constant.instance.FulfillmentType;
import com.dfire.soa.order.dto.OrderTagData;
import com.dfire.soa.order.service.IWaitingOrderWrapperService;
import com.dfire.soa.shop.bo.Shop;
import com.dfire.soa.turtle.service.IConfigService;
import com.dfire.tc.constants.ReqFrom;
import com.dfire.tc.param.FulfillmentParam;
import com.dfire.tc.param.bill.OrderParam;
import com.dfire.tc.param.bill.PayParam;
import com.dfire.tc.param.bill.PromotionParam;
import com.dfire.tc.param.bill.TipFeeParam;
import com.dfire.tc.param.cart.CartInstance;
import com.dfire.tc.param.cart.CartParam;
import com.dfire.tc.param.order.SubmitOrderParam;
import com.dfire.tc.param.order.SubmitPayParam;
import com.dfire.tc.request.bill.CalcBillRequest;
import com.dfire.tc.request.bill.PayBillRequest;
import com.dfire.tc.request.bill.PayCancelRequest;
import com.dfire.tc.request.order.ModifyOrderRequest;
import com.dfire.tc.request.order.SubmitOrderRequest;
import com.dfire.tc.response.bill.CalcBillResponse;
import com.dfire.tc.response.bill.PayBillResponse;
import com.dfire.tc.response.order.ModifyOrderResponse;
import com.dfire.tc.response.order.SubmitOrderResponse;
import com.dfire.tc.service.ITradeNewClientService;
import com.dfire.tc.vo.InstanceVo;
import com.dfire.tc.vo.bill.channel.ThirdPayVo;
import com.google.common.collect.Lists;
import com.twodfire.exception.BizException;
import com.twodfire.redis.CodisService;
import com.twodfire.share.result.Result;
import com.twodfire.share.result.ResultMap;
import com.twodfire.share.result.ResultSupport;
import com.twodfire.share.util.ApiResultUtil;
import com.twodfire.share.util.ResultUtil;
import com.twodfire.wechat.api.camera.ErrorMessageEnum;
import com.twodfire.wechat.common.logger.LoggerMarkers;
import com.twodfire.wechat.common.logger.WXLoggerFactory;
import com.twodfire.wechat.constant.Constants;
import com.twodfire.wechat.facade.ShopClientFacade;
import com.twodfire.wechat.service.IActivityService;
import com.twodfire.wechat.service.IMultipleMenuAuthorityService;
import com.twodfire.wechat.service.IShopService;
import com.twodfire.wechat.service.order.UserOrderDetailService;
import com.twodfire.wechat.utils.IpUtil;
import com.twodfire.wechat.vo.menu.MultipleMenuAuthorityVo;
import com.twodfire.wechat.vo.order.*;
import com.twodfire.wechat.vo.shop.ShopDistanceBaseInfoVo;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiOperation;
import com.wordnik.swagger.annotations.ApiParam;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 订单/账单/支付 NEW
 *
 * <AUTHOR>
 */
@Api(value = "order(订单)")
@RestController
@RequestMapping("/orders/v2")
public class Order2Controller extends BaseController {
    @Resource
    private CodisService codisCommonService;
    @Resource
    private ITradeNewClientService tradeNewClientService;
    @Resource
    private ShopClientFacade shopClientFacade;
    @Resource
    private IConfigService configService;
    @Resource
    private IShopService shopService;
    @Resource
    private UserOrderDetailService userOrderDetailService;
    @Resource
    private IWaitingOrderWrapperService waitingOrderWrapperService;
    @Resource
    private ICartCenterService cartCenterService;
    @Resource
    private IOrderService orderServiceSoaImpl;
    @Resource
    private IActivityService activityService;

    @Resource
    private IMultipleMenuAuthorityService multipleMenuAuthorityService;

    @ApiOperation(value = "计算账单", response = ResultMap.class)
    @PostMapping("/calc_bill")
    public ResultMap calcBill(@RequestBody OrderBillCalcReq2VO req) {
        //param query
        if (StringUtils.isBlank(req.getShopEntityId())) req.setShopEntityId(req.getEntityId());
        SessionUserInfo sessionUserInfo = sessionHelper.getSessionUserInfo(httpServletRequest);
        Shop shop = shopClientFacade.queryShopBaseInfo(req.getEntityId());
        //tc dubbo param define
        CalcBillRequest calcBillRequest = new CalcBillRequest();
        //param fill
        calcBillRequest.setEntityId(req.getEntityId());
        calcBillRequest.setFromVersion(req.getFromVersion());
        fillOrderParam2Bill(calcBillRequest, sessionUserInfo, req);
        fillFulfillmentParam2Bill(calcBillRequest, req);
        fillPromotionParam2Bill(calcBillRequest, req);
        fillPayParam2Bill(calcBillRequest, req);
        fillCartParam2Bill(calcBillRequest, sessionUserInfo, req);
        fillTipFeeParam2Bill(calcBillRequest,req);
        fillBizParam2Bill(calcBillRequest, req);


        //tc dubbo request
        LoggerUtil.info(WXLoggerFactory.BILL_LOGGER, LoggerMarkers.GET_BILL, "calc_bill success, request:{}", JSON.toJSONString(calcBillRequest));
        Result<CalcBillResponse> calcBillResult = tradeNewClientService.calcBill(calcBillRequest);
        //tc dubbo response
        if (ResultUtil.isModelNull(calcBillResult)) {
            LoggerUtil.warn(WXLoggerFactory.BILL_LOGGER, LoggerMarkers.GET_BILL, "calc_bill failed, entityId:{},customerId:{},result:{}", req.getEntityId(), sessionUserInfo.getCustomerId(), JSON.toJSONString(calcBillResult));
            return ApiResultUtil.failResult(calcBillResult.getI18nCode(), calcBillResult.getMessage());
        }
        //success return
        LoggerUtil.info(WXLoggerFactory.BILL_LOGGER, LoggerMarkers.GET_BILL, "calc_bill success, entityId:{},customerId:{},response:{}", req.getEntityId(), sessionUserInfo.getCustomerId(), JSON.toJSONString(calcBillResult.getModel()));

        String lang = StringUtils.isBlank(sessionHelper.getConsumerLang(httpServletRequest)) ? sessionUserInfo.getLang() : sessionHelper.getConsumerLang(httpServletRequest);
        if (StringUtils.isNotBlank(lang) && !LangConstant.ZHCN.equals(lang)) {
            translateInstanceVoInfo(req.getEntityId(), calcBillResult.getModel().getOrderResult().getInstances(), lang);
        }
        return ApiResultUtil.successResult(calcBillResult.getModel());
    }


    private void translateInstanceVoInfo(String entityId, List<InstanceVo> instances, String lang) {
        Set<GooderTransEarlier> earlierList = instances.stream()
                .flatMap(instanceVo -> Stream.concat(Stream.of(instanceVo), Optional.ofNullable(instanceVo.getChildren()).orElse(Collections.emptyList()).stream()))
                .filter(Objects::nonNull)
                .map(instanceVo -> new GooderTransEarlier(
                        instanceVo.getMenuId(),
                        instanceVo.getMakeId(),
                        instanceVo.getSpecDetailId(),
                        instanceVo.getAccountUnit(),
                        instanceVo.getUnit()
                ))
                .collect(Collectors.toSet());

        Result<Map<String, GoodsTransLater>> menuTranslateResult = orderServiceSoaImpl.translateMenu(entityId, lang, Lists.newArrayList(earlierList));
        if (menuTranslateResult.isSuccess() && menuTranslateResult.getModel() != null) {
            Map<String, GoodsTransLater> map = menuTranslateResult.getModel();
            instances.forEach(instanceVo -> {
                translate(instanceVo, map.get(instanceVo.getMenuId()));
                //套餐子菜
                if (instanceVo.getChildren() != null) {
                    instanceVo.getChildren().forEach(child -> translate(child, map.get(child.getMenuId())));
                }
            });
        }
    }


    private void translate(InstanceVo instanceVo, GoodsTransLater goodsTransLater) {
        if (Objects.isNull(goodsTransLater) || Objects.isNull(instanceVo)) {
            return;
        }
        if (StringUtils.isNotEmpty(goodsTransLater.getName())) {
            instanceVo.setName(goodsTransLater.getName());
        }
        if (StringUtils.isNotEmpty(goodsTransLater.getSpecName())) {
            instanceVo.setSpecName(goodsTransLater.getSpecName());
        }
        if (StringUtils.isNotEmpty(goodsTransLater.getMakeName())) {
            instanceVo.setMakeName(goodsTransLater.getMakeName());
        }
        if (StringUtils.isNotEmpty(goodsTransLater.getBuyAccount())) {
            instanceVo.setAccountUnit(goodsTransLater.getBuyAccount());
        }
        if (StringUtils.isNotEmpty(goodsTransLater.getAccount())) {
            instanceVo.setUnit(goodsTransLater.getAccount());
        }
    }

    /**
     * 校验账单 目前只支持校验立即下单传入的商品 目前只支持校验套餐
     *
     * @param req
     * @return
     */
    @ApiOperation(value = "校验账单商品", response = ResultMap.class)
    @PostMapping("/check_bill")
    public ResultMap checkBill(@RequestBody OrderBillCalcReq2VO req) {
        //param query
        if (StringUtils.isBlank(req.getShopEntityId())) req.setShopEntityId(req.getEntityId());
        SessionUserInfo sessionUserInfo = sessionHelper.getSessionUserInfo(httpServletRequest);
        CalcBillRequest calcBillRequest = new CalcBillRequest();
        fillCartParam2Bill(calcBillRequest, sessionUserInfo, req);
        checkCart2Bill(calcBillRequest, sessionUserInfo);
        return ApiResultUtil.successResult(1);
    }

    /**
     * 提交订单
     *
     * @param req 参数
     * @return resp
     */
    @ApiOperation(value = "提交订单", response = ResultMap.class)
    @PostMapping("/submit_order")
    public ResultMap submitOrder(@RequestBody OrderSubmitReq2VO req) {
        //param query
        if (StringUtils.isBlank(req.getShopEntityId())) req.setShopEntityId(req.getEntityId());
        SessionUserInfo sessionUserInfo = sessionHelper.getSessionUserInfo(httpServletRequest);
//        Shop shop = shopClientFacade.queryShopBaseInfo(req.getEntityId());
        // check multiple menu authority and time
        if (Objects.nonNull(req.getCartParam()) &&Objects.nonNull(req.getBizParam())) {
            Integer scene = Objects.isNull(req.getBizParam().get("scene")) ? null : Integer.valueOf((String) req.getBizParam().get("scene"));
            String multipleMenuId = Objects.isNull(req.getCartParam().get("multipleMenuId")) ? null :(String) req.getCartParam().get("multipleMenuId");
            MultipleMenuAuthorityVo multipleMenuAuthorityVo = multipleMenuAuthorityService.checkMultipleMenuAuthority(scene ,
                    req.getEntityId(),sessionUserInfo.getCustomerId(),multipleMenuId);
            if (multipleMenuAuthorityVo.getExceptionFlow() > 0) {
                return ApiResultUtil.failResult(WeixinMealCode.MULTI_WMI003.getErrCode(), WeixinMealCode.MULTI_WMI003.getMultiCode());
            }
            if (multipleMenuAuthorityVo.getSupportRead() == 2) {
                // 特殊的code
                return ApiResultUtil.failResult(ErrorMessageEnum.MULTI_WMI278.getErrCode(), multipleMenuAuthorityVo.getNotSupportReadReason());
            }
        }

        //tc dubbo param define
        SubmitOrderRequest submitOrderRequest = new SubmitOrderRequest();
        //param fill order fulfillment cart biz
        fillFulfillmentParam2Submit(submitOrderRequest, sessionUserInfo, req);
        fillOrderParam2Submit(submitOrderRequest, sessionUserInfo, req);
        fillCartParam2Submit(submitOrderRequest, sessionUserInfo, req);
        fillBizParam2Submit(submitOrderRequest, req);
        fillPayParam2Submit(submitOrderRequest, req);
        fillPromotionParam2Submit(submitOrderRequest, req);
        TipFeeParam tipFeeParam = Optional.ofNullable(JSONObject.parseObject(JSON.toJSONString(req.getTipFeeParam()), TipFeeParam.class)).orElse(new TipFeeParam());
        submitOrderRequest.setTipFeeParam(tipFeeParam);
        String tagCacheKey;
        if (StringUtils.isNotBlank(submitOrderRequest.getOrderParam().getSeatCode())) {
            tagCacheKey = Constants.CacheKey.TABLE_ORDER_TAG_PREFIX + submitOrderRequest.getOrderParam().getEntityId() + ":" + submitOrderRequest.getOrderParam().getSeatCode();
        } else {
            tagCacheKey = Constants.CacheKey.TABLE_ORDER_TAG_PREFIX + submitOrderRequest.getOrderParam().getEntityId() + ":" + submitOrderRequest.getCartParam().getCustomerRegisterId();
        }
        String alipayNFCTagId = null;
        if (Objects.nonNull(req.getBizParam())) {
            alipayNFCTagId = Objects.isNull(req.getBizParam().get("alipayNFCTagId")) ? null : Convert.asString(req.getBizParam().get("alipayNFCTagId"), null);
        }
        fillOrderParamTag(tagCacheKey , alipayNFCTagId, submitOrderRequest);
        //tc dubbo request
        LoggerUtil.info(WXLoggerFactory.ORDER_LOGGER, LoggerMarkers.SUBMIT_ORDER, "submit_order success, request:{}", JSON.toJSONString(submitOrderRequest));
        Result<SubmitOrderResponse> submitOrderResult = tradeNewClientService.submitOrder(submitOrderRequest);
        // 清除打标缓存
        if (submitOrderResult != null && submitOrderResult.isSuccess()) {
            String tagCacheNumKey = tagCacheKey + ":num";
            codisCommonService.del(tagCacheKey);
            codisCommonService.del(tagCacheNumKey);
        }

        //tc dubbo response
        if (ResultUtil.isModelNull(submitOrderResult)) {
            LoggerUtil.warn(WXLoggerFactory.ORDER_LOGGER, LoggerMarkers.SUBMIT_ORDER, "submit_order failed, entityId:{},customerId:{},result:{}", req.getEntityId(), sessionUserInfo.getCustomerId(), JSON.toJSONString(submitOrderResult));
            return ApiResultUtil.failResult(submitOrderResult.getI18nCode(), submitOrderResult.getMessage());
        }
        if (submitOrderRequest.getOrderParam().getOrderFrom() != null && submitOrderRequest.getOrderParam().getOrderFrom() == 99) {
            //todo
        }
        //success return
        LoggerUtil.info(WXLoggerFactory.ORDER_LOGGER, LoggerMarkers.SUBMIT_ORDER, "submit_order success, entityId:{},customerId:{},response:{}", req.getEntityId(), sessionUserInfo.getCustomerId(), JSON.toJSONString(submitOrderResult.getModel()));
        return ApiResultUtil.successResult(submitOrderResult.getModel());
    }

    private void fillPayParam2Submit(SubmitOrderRequest submitOrderRequest, OrderSubmitReq2VO req) {
        SubmitPayParam orderParam = Optional.ofNullable(JSONObject.parseObject(JSON.toJSONString(req.getPayParam()), SubmitPayParam.class)).orElse(new SubmitPayParam());
        submitOrderRequest.setPayParam(orderParam);
    }


    private void fillOrderParamTag (String tagCacheKey, String alipayNFCTagId, SubmitOrderRequest request) {
        List<OrderTagData> data = Lists.newArrayList();
        //可口可乐打标
        // 活动打标
        activityService.orderActivityExtMark(tagCacheKey, request, data);
        activityService.orderAlipayNFCMark(alipayNFCTagId, request, data);
        if (CollectionUtils.isNotEmpty(data)) {
            request.getOrderParam().setTags(data);
        }
    }
    /**
     * 支付账单
     *
     * @param req 参数
     * @return resp
     */
    @ApiOperation(value = "支付账单", response = ResultMap.class)
    @PostMapping("/pay_bill")
    public ResultMap payBill(@RequestBody OrderBillPayReq2VO req) {
        //param query
        if (StringUtils.isBlank(req.getShopEntityId())) req.setShopEntityId(req.getEntityId());
        if (StringUtils.isNotBlank(httpServletRequest.getParameter("plugin_token")))
            req.setPluginToken(httpServletRequest.getParameter("plugin_token"));
        SessionUserInfo sessionUserInfo = sessionHelper.getSessionUserInfo(httpServletRequest);
        SessionUserInfo sessionUserInfoTrade = sessionUserInfo;
        if (StringUtils.isNotBlank(req.getPluginToken())) {
            SessionUserInfo sessionByToken = sessionHelper.getSessionByToken(req.getPluginToken());
            if (Objects.nonNull(sessionByToken)) {
                sessionUserInfoTrade = sessionByToken;
            }
        }

        Shop shop = shopClientFacade.queryShopBaseInfo(req.getEntityId());
        //tc dubbo param define
        PayBillRequest payBillRequest = new PayBillRequest();
        //param fill order fulfillment cart biz
        fillPayBillBase2Pay(payBillRequest, sessionUserInfo, sessionUserInfoTrade, req);
//        //todo: 测试代码
//        fillCardPay(payBillRequest,sessionUserInfoTrade,req);
        fillThirdPayParam2Pay(payBillRequest, sessionUserInfoTrade, req);
        payBillRequest.setPromotionParam(req.getPromotionParam());
        payBillRequest.setCollectPayMode(req.getCollectPayMode());
        payBillRequest.setMergeOrder(req.getMergeOrder());
        payBillRequest.setRequestFrom(ReqFrom.USER_CLIENT.getOpValue());

        //tc dubbo request
        LoggerUtil.info(WXLoggerFactory.PAY_LOGGER, LoggerMarkers.PAY, "pay_bill success, request:{}", JSON.toJSONString(payBillRequest));
        Result<PayBillResponse> payBillResult = tradeNewClientService.payBill(payBillRequest);
        //tc dubbo response
        if (ResultUtil.isModelNull(payBillResult)) {
            LoggerUtil.warn(WXLoggerFactory.PAY_LOGGER, LoggerMarkers.PAY, "pay_bill failed, entityId:{},customerId:{},result:{}", req.getEntityId(), sessionUserInfo.getCustomerId(), JSON.toJSONString(payBillResult));
            return ApiResultUtil.failResult(payBillResult.getI18nCode(), payBillResult.getMessage());
        }
        //success return
        LoggerUtil.info(WXLoggerFactory.PAY_LOGGER, LoggerMarkers.PAY, "pay_bill success, entityId:{},customerId:{},response:{}", req.getEntityId(), sessionUserInfo.getCustomerId(), JSON.toJSONString(payBillResult.getModel()));

        //去除提醒状态
        codisCommonService.del(Constants.CacheKey.ONLINE_CART_REMIND_PREFIX + req.getEntityId() + "_" + sessionUserInfo.getCustomerId());
        return ApiResultUtil.successResult(payBillResult.getModel());
    }


    /**
     * 删除订单
     *
     * @return resp
     */
    @ApiOperation(value = "删除订单", response = ResultMap.class)
    @PostMapping("/delete_my_order")
    public ResultMap deleteMyOrder(HttpServletRequest request,
                                   @ApiParam(value = "订单id", required = false)
                                   @RequestParam(value = "orderId", required = false) String orderId,
                                   @ApiParam(value = "订单id", required = false)
                                   @RequestParam(value = "waitingOrderId", required = false) String waitingOrderId,
                                   @ApiParam(value = "门店id", required = true)
                                   @RequestParam(value = "entityId", required = true) String entityId) {
        SessionUserInfo sessionUserInfo = sessionHelper.getSessionUserInfo(request);
        Result<ModifyOrderResponse> modifyOrder = null;

        if (StringUtils.isNotBlank(orderId)) {
            ModifyOrderRequest modifyOrderRequest = new ModifyOrderRequest();
            modifyOrderRequest.setOrderId(orderId);
            modifyOrderRequest.setEntityId(entityId);
            modifyOrderRequest.setOpUserId(sessionUserInfo.getCustomerId());
            modifyOrderRequest.setOpUserName(sessionUserInfo.getNickname());
            modifyOrderRequest.setModifyType(ModifyOrderRequest.USER_HIDE);

            modifyOrder = tradeNewClientService.modifyOrder(modifyOrderRequest);
        }
        if (StringUtils.isNotBlank(waitingOrderId)) {
            modifyOrder = new ResultSupport<>();
            ModifyOrderResponse modifyOrderResponse = new ModifyOrderResponse();
            Optional<WaitingOrder> optional = userOrderDetailService.getWaitingOrder(entityId, waitingOrderId);
            if (optional.isPresent()) {
                WaitingOrder waitingOrder = optional.get();
                waitingOrder.setIsValid((short) 0);
                Result<Integer> waiting = waitingOrderWrapperService.dynamicUpdate(waitingOrder);
                if (Objects.nonNull(waiting) && waiting.isSuccess()) {
                    if (waiting.getModel() != null && waiting.getModel() > 0) {
                        modifyOrderResponse.setSuccess(true);
                    } else {
                        modifyOrderResponse.setSuccess(false);
                        modifyOrderResponse.setMessage(ErrorMessageEnum.MULTI_WMI283.getMessage());
                        modifyOrder.setI18nCode(ErrorMessageEnum.MULTI_WMI283.getMultiCode());
                    }
                }
            }
            modifyOrder.setModel(modifyOrderResponse);
        }

        if (Objects.isNull(modifyOrder)) {
            return ApiResultUtil.failResult(ErrorMessageEnum.MULTI_WMI283.getMultiCode(), ErrorMessageEnum.MULTI_WMI283.getMessage());
        }
        //tc dubbo response
        if (Objects.nonNull(modifyOrder) && Objects.nonNull(modifyOrder.getModel()) && !modifyOrder.getModel().isSuccess()) {
            LoggerUtil.warn(WXLoggerFactory.ORDER_LOGGER, LoggerMarkers.GET_OWN_ORDER, "modifyOrder hide failed, entityId:{},customerId:{},result:{}"
                    , entityId, sessionUserInfo.getCustomerId(), JSON.toJSONString(modifyOrder));
            return ApiResultUtil.failResult(modifyOrder.getI18nCode(), modifyOrder.getModel().getMessage());
        }

        return ApiResultUtil.successResult(modifyOrder.getModel());
    }


    @ApiOperation(value = "核销预定订单", response = ResultMap.class)
    @PostMapping("/reservation_verify")
    public ResultMap reservationVerify(HttpServletRequest request,
                                       @ApiParam(value = "订单id", required = true)
                                       @RequestParam(value = "order_id", required = true) String orderId,
                                       @ApiParam(value = "门店id", required = true)
                                       @RequestParam(value = "entity_id", required = true) String entityId) {

        if (StringUtils.isBlank(orderId) || StringUtils.isBlank(entityId)) {
                return ApiResultUtil.failResult(PublicCode.MULTI_000072.getErrCode(), PublicCode.MULTI_000072.getMultiCode());
            }
            SessionUserInfo sessionUserInfo = sessionHelper.getSessionUserInfo(request);
            ModifyOrderRequest modifyOrderRequest = new ModifyOrderRequest();
            modifyOrderRequest.setModifyType(ModifyOrderRequest.VERIFY);
            modifyOrderRequest.setOrderId(orderId);
            modifyOrderRequest.setEntityId(entityId);
            modifyOrderRequest.setOpUserId(sessionUserInfo.getCustomerId());
            modifyOrderRequest.setOpUserName(sessionUserInfo.getNickname());
            modifyOrderRequest.setClientFrom(Integer.valueOf(ClientFromConstant.CLIENT_FROM_MINI_PROGRAM));
            Result<ModifyOrderResponse> result = tradeNewClientService.modifyOrder(modifyOrderRequest);
            if(ResultUtil.isModelNotNull(result)) {
                ModifyOrderResponse resultModel = result.getModel();
                if (resultModel.isSuccess()) {
                    return ApiResultUtil.successResult(resultModel);
                }
                return ApiResultUtil.failResult(resultModel.getErrCode(),resultModel.getMessage());
            }
            if (Objects.nonNull(result)) {
                return ApiResultUtil.failResult(result.getI18nCode(),result.getMessage());
            }

            return ApiResultUtil.failResult(PublicCode.MULTI_101550.getErrCode(), PublicCode.MULTI_101550.getMultiCode());
        }


    /**
     * 支付账单 填充PayBillBaseParam
     *
     * @param payBillRequest  tc 请求
     * @param sessionUserInfo session
     * @param req             req
     */
    private void fillPayBillBase2Pay(PayBillRequest payBillRequest, SessionUserInfo sessionUserInfo, SessionUserInfo sessionUserInfoTrade, OrderBillPayReq2VO req) {
        payBillRequest.setBillId(req.getBillId());
        payBillRequest.setOrderId(req.getOrderId());
        payBillRequest.setEntityId(req.getEntityId());
        payBillRequest.setOpUserId(sessionUserInfo.getCustomerId());
        payBillRequest.setOpUserName(sessionUserInfo.getNickname());
        payBillRequest.setCustomerRegisterId(sessionUserInfo.getCustomerId());
        payBillRequest.setClientFrom(getClientOrderFrom(sessionUserInfoTrade).getClientFrom());
        payBillRequest.setRequestFrom(ReqFrom.USER_CLIENT.getOpValue());
        //设置waitingOrderId
        payBillRequest.setWaitingOrderId(req.getWaitingOrderId());
        if (StringUtils.isNotBlank(req.getThirdBindEntityId())) {
            HashMap<String, Object> bizMap = new HashMap<>();
            bizMap.put("thirdBindEntityId", req.getThirdBindEntityId());
            payBillRequest.setBizParamMap(bizMap);
        }
    }

    /**
     * 支付账单 填充ThirdPayParam
     *
     * @param payBillRequest tc 请求
     * @param req            req
     */
    private void fillThirdPayParam2Pay(PayBillRequest payBillRequest, SessionUserInfo sessionUserInfo, OrderBillPayReq2VO req) {
        ThirdPayVo thirdPayVo = Optional.ofNullable(JSONObject.parseObject(JSON.toJSONString(req.getThirdPayVo()), ThirdPayVo.class)).orElse(new ThirdPayVo());
        payBillRequest.setThirdPayVo(thirdPayVo);
        if (isApplet(sessionUserInfo.getClientSource())) {
            thirdPayVo.setOpenId(sessionUserInfo.getMiniProgramMealOpenid());
            thirdPayVo.setLargeOpenid(sessionUserInfo.getMiniProgramMealOpenid());
        }
        if (StringUtils.isBlank(thirdPayVo.getOpenId())) {
            thirdPayVo.setOpenId(sessionUserInfo.getOpenid());
        }
        if (StringUtils.isBlank(thirdPayVo.getLargeOpenid())) {
            thirdPayVo.setLargeOpenid(sessionUserInfo.getLargeOpenid());
        }
        thirdPayVo.setSubAppId(StringUtils.isNotBlank(sessionUserInfo.getAppId()) ? sessionUserInfo.getAppId() : req.getSubAppId());
        thirdPayVo.setRemoteAddr(IpUtil.getRemoteIp(httpServletRequest));
    }


//    private void fillCardPay(PayBillRequest payBillRequest, SessionUserInfo sessionUserInfo, OrderBillPayReq2VO req) {
//        PayParam payParam = req.getPayParam();
//        if(payParam != null ){
//            List<CardPayVo> cardPays = payParam.getCardPays();
//            if(CollectionUtils.isNotEmpty(cardPays)){
//                List<CardPayVo> collect = cardPays.stream().filter(x -> x.isSelected()).collect(Collectors.toList());
//                payParam.setCardPays(collect);
//            }
//        }
//        payBillRequest.setPayParam(payParam);
//
//    }

    /**
     * 提交订单 填充OrderParam
     *
     * @param submitOrderRequest tc 请求
     * @param sessionUserInfo    session
     * @param req                req
     */
    private void fillOrderParam2Submit(SubmitOrderRequest submitOrderRequest, SessionUserInfo sessionUserInfo, OrderSubmitReq2VO req) {
        SubmitOrderParam orderParam = Optional.ofNullable(JSONObject.parseObject(JSON.toJSONString(req.getOrderParam()), SubmitOrderParam.class)).orElse(new SubmitOrderParam());
        submitOrderRequest.setOrderParam(orderParam);
        OrderClientFrom2VO orderClientFromVO = getClientOrderFrom(sessionUserInfo);
        orderParam.setEntityId(req.getEntityId());
        orderParam.setOpUserId(sessionUserInfo.getCustomerId());
        orderParam.setOpUserName(sessionUserInfo.getNickname());
        orderParam.setClientFrom(orderClientFromVO.getClientFrom());
        orderParam.setRequestFrom(ReqFrom.USER_CLIENT.getOpValue());
        /**
         * @modiy by beifengjun
         * 增加了自提手机号如果前端没传也不用默认
         */
        boolean isTakeSelf = Objects.nonNull(submitOrderRequest.getFulfillmentParam())
                && Objects.equals(submitOrderRequest.getFulfillmentParam().getFulfillmentType(), FulfillmentType.TAKE_ONSELF);

        if(isTakeSelf) {
            return;
        }
        String reqMobile = orderParam.getMobile();
        if (StringUtils.isBlank(reqMobile)) {
            orderParam.setMobile(sessionUserInfo.getMobile());
        }
    }

    /**
     * 提交订单 填充FulfillmentParam
     *
     * @param submitOrderRequest tc 请求
     * @param req                req
     */
    private void fillFulfillmentParam2Submit(SubmitOrderRequest submitOrderRequest, SessionUserInfo sessionUserInfo, OrderSubmitReq2VO req) {
        FulfillmentParam fulfillmentParam = Optional.ofNullable(JSONObject.parseObject(JSON.toJSONString(req.getFulfillmentParam()), FulfillmentParam.class)).orElse(new FulfillmentParam());
        submitOrderRequest.setFulfillmentParam(fulfillmentParam);
        if (StringUtils.isBlank(fulfillmentParam.getCustomerRegisterId())) {
            fulfillmentParam.setCustomerRegisterId(sessionUserInfo.getCustomerId());
        }
    }

    /**
     * 提交订单 填充CartParam
     *
     * @param submitOrderRequest tc 请求
     * @param sessionUserInfo    session
     * @param req                req
     */
    private void fillCartParam2Submit(SubmitOrderRequest submitOrderRequest, SessionUserInfo sessionUserInfo, OrderSubmitReq2VO req) {
        CartParam cartParam = Optional.ofNullable(JSONObject.parseObject(JSON.toJSONString(req.getCartParam()), CartParam.class)).orElse(new CartParam());
        submitOrderRequest.setCartParam(cartParam);
        fillCartInstance(cartParam.getInstance());
        cartParam.setEntityId(req.getEntityId());
        cartParam.setCustomerRegisterId(sessionUserInfo.getCustomerId());
    }

    /**
     * 提交订单 填充BizParam
     *
     * @param submitOrderRequest tc 请求
     * @param req                req
     */
    private void fillBizParam2Submit(SubmitOrderRequest submitOrderRequest, OrderSubmitReq2VO req) {
        Map<String, Object> bizParam = Optional.ofNullable(req.getBizParam()).orElse(new HashMap<>());
        submitOrderRequest.setBizParam(bizParam);
    }

    /**
     * 提交订单 填充promotionParam
     *
     * @param submitOrderRequest submitOrderRequest
     * @param req                req
     */
    private void fillPromotionParam2Submit(SubmitOrderRequest submitOrderRequest, OrderSubmitReq2VO req) {
        PromotionParam promotionParam = Optional.ofNullable(JSONObject.parseObject(JSON.toJSONString(req.getPromotionParam()), PromotionParam.class)).orElse(new PromotionParam());
        submitOrderRequest.setPromotionParam(promotionParam);
    }

    /**
     * 获取账单 填充OrderParam
     *
     * @param calcBillRequest tc 请求
     * @param sessionUserInfo session
     * @param req             req
     */
    private void fillOrderParam2Bill(CalcBillRequest calcBillRequest, SessionUserInfo sessionUserInfo, OrderBillCalcReq2VO req) {
        OrderParam orderParam = Optional.ofNullable(JSONObject.parseObject(JSON.toJSONString(req.getOrderParam()), OrderParam.class)).orElse(new OrderParam());
        calcBillRequest.setOrderParam(orderParam);
        OrderClientFrom2VO orderClientFromVO = getClientOrderFrom(sessionUserInfo);
        orderParam.setCustomerRegisterId(sessionUserInfo.getCustomerId());
        orderParam.setClientFrom(orderClientFromVO.getClientFrom());
        orderParam.setRequestUA(httpServletRequest.getHeader(REQUEST_UA));
        orderParam.setRequestFrom(ReqFrom.USER_CLIENT.getOpValue());
    }

    /**
     * 获取账单 填充FulfillmentParam
     *
     * @param calcBillRequest tc 请求
     * @param req             req
     */
    private void fillFulfillmentParam2Bill(CalcBillRequest calcBillRequest, OrderBillCalcReq2VO req) {
        FulfillmentParam fulfillmentParam = Optional.ofNullable(JSONObject.parseObject(JSON.toJSONString(req.getFulfillmentParam()), FulfillmentParam.class)).orElse(new FulfillmentParam());
        calcBillRequest.setFulfillmentParam(fulfillmentParam);
    }

    /**
     * 获取账单 填充PromotionParam
     *
     * @param calcBillRequest tc 请求
     * @param req             req
     */
    private void fillPromotionParam2Bill(CalcBillRequest calcBillRequest, OrderBillCalcReq2VO req) {
        PromotionParam promotionParam = Optional.ofNullable(JSONObject.parseObject(JSON.toJSONString(req.getPromotionParam()), PromotionParam.class)).orElse(new PromotionParam());
        calcBillRequest.setPromotionParam(promotionParam);
    }

    /**
     * 获取账单 填充PayParam
     *
     * @param calcBillRequest tc 请求
     * @param req             req
     */
    private void fillPayParam2Bill(CalcBillRequest calcBillRequest, OrderBillCalcReq2VO req) {
        PayParam payParam = Optional.ofNullable(JSONObject.parseObject(JSON.toJSONString(req.getPayParam()), PayParam.class)).orElse(new PayParam());
        calcBillRequest.setPayParam(payParam);
    }
    private void fillTipFeeParam2Bill(CalcBillRequest calcBillRequest, OrderBillCalcReq2VO req) {
        TipFeeParam tipFeeParam = Optional.ofNullable(JSONObject.parseObject(JSON.toJSONString(req.getTipFeeParam()), TipFeeParam.class)).orElse(new TipFeeParam());
        calcBillRequest.setTipFeeParam(tipFeeParam);
    }
    /**
     * 获取账单 填充CartParam
     *
     * @param calcBillRequest tc 请求
     * @param sessionUserInfo session
     * @param req             req
     */
    private void fillCartParam2Bill(CalcBillRequest calcBillRequest, SessionUserInfo sessionUserInfo, OrderBillCalcReq2VO req) {
        CartParam cartParam = Optional.ofNullable(JSONObject.parseObject(JSON.toJSONString(req.getCartParam()), CartParam.class)).orElse(null);
        calcBillRequest.setCartParam(cartParam);
        if (Objects.nonNull(cartParam)) {
            fillCartInstance(cartParam.getInstance());
            cartParam.setEntityId(req.getEntityId());
            cartParam.setCustomerRegisterId(sessionUserInfo.getCustomerId());
        }

    }

    /**
     * 填充CartInstance中的Type From GoodsType
     *
     * @param instances 购物车商品
     */
    private void fillCartInstance(List<CartInstance> instances) {
//        if (!CollectionUtils.isEmpty(instances)) {
//            instances.forEach(instance -> {
//                if (Objects.isNull(instance.getAccountNum())) instance.setAccountNum(instance.getNum());
//                fillCartInstance(instance.getChildren());
//            });
//        }
    }

    /**
     * 校验购物车
     *
     * @param calcBillRequest
     */
    private void checkCart2Bill(CalcBillRequest calcBillRequest, SessionUserInfo sessionUserInfo) {
        CartParam cartParam = calcBillRequest.getCartParam();
        if (cartParam.getUseCloudCart()) {
            return;
        }
        Result result = cartCenterService.checkSuit(transToCartItem(cartParam.getInstance()), cartParam.getEntityId());
        if (!ResultUtil.isResultSuccess(result)) {
            LoggerUtil.warn(WXLoggerFactory.BILL_LOGGER, LoggerMarkers.GET_BILL, "check_bill failed, entityId:{},customerId:{},result:{}", cartParam.getEntityId(), sessionUserInfo.getCustomerId(), JSON.toJSONString(result));
            throw new BizException(result.getMessage(), result.getResultCode());
        }
    }

    private List<CartItem> transToCartItem(List<CartInstance> instances) {
        return Optional.ofNullable(instances).orElse(Collections.emptyList()).stream().map(ins -> {
            CartItem cartItem = new CartItem();
            cartItem.setIndex(ins.getId());
            cartItem.setMenuId(ins.getMenuId());
            cartItem.setMultiMenuId(ins.getMultipleMenuId());
            cartItem.setSuitMenuDetailId(ins.getSuitMenuDetailId());
            cartItem.setSkuId(ins.getSkuId());
            cartItem.setSpecId(ins.getSpecDetailId());
            cartItem.setMakeId(ins.getMakeId());
            cartItem.setNum(ins.getNum());
            cartItem.setGoodsType(ins.getGoodsType());
            cartItem.setKindType(Optional.ofNullable(ins.getKind()).map(Short::intValue).orElse(1));
            cartItem.setChildCartVos(transToCartItem(ins.getChildren()));
            return cartItem;
        }).collect(Collectors.toList());
    }

    /**
     * 获取账单 填充BizParam
     *
     * @param calcBillRequest tc 请求
     * @param req             req
     */
    private void fillBizParam2Bill(CalcBillRequest calcBillRequest, OrderBillCalcReq2VO req) {
        Map<String, Object> bizParam = Optional.ofNullable(req.getBizParam()).orElse(new HashMap<>());
        calcBillRequest.setBizParamMap(bizParam);
    }



    /**
     * 是否小程序来源
     *
     * @param clientSource source
     * @return is applet
     */
    private Boolean isApplet(String clientSource) {
        return isWxApplet(clientSource) || isAliPayApplet(clientSource);
    }

    /**
     * 是否微信小程序来源
     *
     * @param clientSource source
     * @return is wx applet
     */
    private Boolean isWxApplet(String clientSource) {
        return StringUtils.equalsIgnoreCase(clientSource, ClientSourceEnum.MINI_PROGRAM_CLIENT.getCode()) || StringUtils.equalsIgnoreCase(clientSource, ClientSourceEnum.HPP_MINI_PROGRAM_CLIENT.getCode());
    }

    /**
     * 是否支付宝来源
     *
     * @param clientSource source
     * @return is ali applet
     */
    private Boolean isAliPayApplet(String clientSource) {
        return StringUtils.equalsIgnoreCase(clientSource, ClientSourceEnum.ALIPAY_MINI_PROGRAM_CLIENT.getCode());
    }

    @ApiOperation(value = "对当前门店和配送地址进行二次确认", response = ResultMap.class)
    @PostMapping("/check")
    public ResultMap check(HttpServletRequest request, @RequestBody CheckOrderVo checkOrderVo) throws Exception {

        boolean required = false;
        CheckOrderStatusVo<ShopDistanceBaseInfoVo> vo = new CheckOrderStatusVo<>();
        if (CheckOrderVo.TYPE_PICK_UP.equals(checkOrderVo.getType()) || CheckOrderVo.TYPE_TAKEOUT.equals(checkOrderVo.getType())) {
            try {
                Result<String> requireCheck = configService.getConfigValByCodeWithDefaultSystemTypeId("ORDER_ADD_REMIND", checkOrderVo.getEntityId());
                required = ResultUtil.isModelNotNull(requireCheck) && "1".equals(requireCheck.getModel());
                if (required && CheckOrderVo.TYPE_PICK_UP.equals(checkOrderVo.getType()) && StringUtils.isNotBlank(checkOrderVo.getLongitude()) && StringUtils.isNotBlank(checkOrderVo.getLatitude())) {
                    SessionUserInfo sessionUserInfo = sessionHelper.getSessionUserInfo(request);
                    String customerId = sessionUserInfo.getCustomerId();
                    ResultMap shopInfoResult = shopService.getShopInfoResult(checkOrderVo.getLatitude(), checkOrderVo.getLongitude(), checkOrderVo.getEntityId(), customerId);
                    if (shopInfoResult.get(ResultMap.DATA) != null) {
                        vo.setData((ShopDistanceBaseInfoVo) shopInfoResult.get(ResultMap.DATA));
                    }
                }
            } catch (Exception e) {
                required = false;
            }
        }
        vo.setRequireCheck(required);
        return ApiResultUtil.successResult(vo);
    }


    @ApiOperation(value = "通知取消", response = ResultMap.class)
    @PostMapping("/notify_cancel_pay")
    public ResultMap notifyCancelPay(HttpServletRequest request, @RequestParam(value = "entity_id", required = true) String entityId,
                               @RequestParam(value = "order_id", required = true) String orderId,
                                @RequestParam(value = "snapshot_id", required = true) String snapshotId) {

        SessionUserInfo sessionUserInfo = sessionHelper.getSessionUserInfo(httpServletRequest);

        PayCancelRequest tcRequest = new PayCancelRequest();
        tcRequest.setEntityId(entityId);
        tcRequest.setOrderId(orderId);
        tcRequest.setSnapshotId(snapshotId);
        tcRequest.setCancelReason("Release");
        tcRequest.setCancelType(PayCancelRequest.RELEASE);
        tcRequest.setOpUserId(sessionUserInfo.getCustomerId());
        tcRequest.setOpUserName(sessionUserInfo.getNickname());
        tradeNewClientService.payCancel(tcRequest);
        return ApiResultUtil.successResult(null);
    }
}
