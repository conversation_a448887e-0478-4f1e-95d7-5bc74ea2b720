package com.twodfire.wechat.api.camera;

import com.alibaba.fastjson.JSON;
import com.dfire.consumer.i18n.code.PublicCode;
import com.dfire.consumer.i18n.code.api.WeixinMealCode;
import com.dfire.consumer.util.LoggerUtil;
import com.dfire.consumer.util.UuidUtil;
import com.dfire.consumer.util.log.LogMarker;
import com.dfire.soa.consumer.fm.dto.VisitorUserQuery;
import com.dfire.soa.consumer.fm.service.IVisitorUserService;
import com.dfire.soa.consumer.fm.vo.VisitorUserVo;
import com.dfire.soa.member.bean.TwodfireMember;
import com.dfire.soa.member.service.ITwodfireMemberService;
import com.dfire.soa.oauth.common.constants.ForeignThirdPlatform;
import com.dfire.soa.oauth.request.OAuthVisitorRequest;
import com.dfire.soa.oauth.request.third.OAuthThirdPageOAuthSignRequest;
import com.dfire.soa.oauth.service.IAuthClientService;
import com.twodfire.share.result.Result;
import com.twodfire.share.result.ResultMap;
import com.twodfire.share.util.ApiResultUtil;
import com.twodfire.share.util.ResultUtil;
import com.twodfire.wechat.api.BaseController;
import com.twodfire.wechat.common.cache.SentinelCacheService;
import com.twodfire.wechat.common.logger.LoggerMarkers;
import com.twodfire.wechat.common.logger.WXLoggerFactory;
import com.twodfire.wechat.constant.Constants;
import com.twodfire.wechat.facade.ApiMobileCheckFacade;
import com.twodfire.wechat.service.ICameraScanService;
import com.twodfire.wechat.utils.CodeGeneratorUtils;
import com.twodfire.wechat.utils.ResultMapUtil;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiOperation;
import com.wordnik.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * @Author: hechuan
 * @Date: 2020/8/5 09:56
 * @Description: 相机扫码登录注册
 */
@Api("相机扫码 api")
@RestController
@RequestMapping("/camera/scan")
public class CameraScanController extends BaseController {


    @Resource
    private SentinelCacheService cacheService;
    @Resource
    private ApiMobileCheckFacade apiMobileCheckFacade;
    @Resource
    private ICameraScanService cameraScanService;
    @Resource
    private ITwodfireMemberService twodfireMemberService;
    @Resource
    protected IAuthClientService authClientService;
    @Resource
    private IVisitorUserService visitorUserService;


    @ApiOperation(value = "获取访客的userId", response = ResultMap.class)
    @GetMapping("/visitor/userId")
    @ResponseStatus(HttpStatus.OK)
    public ResultMap getVerifyCode() {
        return ResultMapUtil.successResult(UuidUtil.getUUID());
    }

    @ApiOperation("访客登陆")
    @PostMapping("/visitor/login")
    @ResponseStatus(HttpStatus.OK)
    public ResultMap visitorLogin(HttpServletRequest request,
                                  @ApiParam(value = "userId", required = true)
                                  @RequestParam(name = "userId", value = "", required = true) String userId) {
        //先插入临时用户表，为了定时删除平台会员的用户。
        VisitorUserQuery query = new VisitorUserQuery();
        query.setVisitorUserId(userId);
        Result<List<VisitorUserVo>> queryResult = visitorUserService.queryVisitorUser(query);
        if (!ResultUtil.isResultSuccess(queryResult)) {
            LoggerUtil.error(WXLoggerFactory.BUSINESS_LOGGER, LoggerMarkers.BUSINESS, "visitorUserService.queryVisitorUser return failed,userId:{},result:{}", userId, JSON.toJSONString(queryResult));
            return ApiResultUtil.failResult(PublicCode.MULTI_101550.getErrCode(), PublicCode.MULTI_101550.getMultiCode(), PublicCode.MULTI_101550.getMessage());
        }
        if (CollectionUtils.isEmpty(queryResult.getModel())) {
            VisitorUserVo userVo = new VisitorUserVo();
            userVo.setVisitorUserId(userId);
            Result<Integer> addVisitorUserResult = visitorUserService.addVisitorUser(userVo);
            if (!ResultUtil.isResultSuccess(addVisitorUserResult)) {
                LoggerUtil.error(WXLoggerFactory.BUSINESS_LOGGER, LoggerMarkers.BUSINESS, "visitorUserService.addVisitorUser return failed,userId:{},result:{}", userId, JSON.toJSONString(addVisitorUserResult));
                return ApiResultUtil.failResult(PublicCode.MULTI_101550.getErrCode(), PublicCode.MULTI_101550.getMultiCode(), PublicCode.MULTI_101550.getMessage());
            }
        }
        //访客登录
        OAuthVisitorRequest visitorRequest = new OAuthVisitorRequest(userId, request);
        Result<String> rs = authClientService.execute(visitorRequest);
        if (!ResultUtil.isResultSuccess(rs)) {
            LoggerUtil.error(WXLoggerFactory.BUSINESS_LOGGER, LoggerMarkers.BUSINESS, "authClientService.bizExecute return failed,userId:{},result:{}", userId, JSON.toJSONString(rs));
            return new ResultMap(rs);
        }
        return ResultMapUtil.successResult(rs.getModel());
    }

    @ApiOperation("相机扫码登录")
    @PostMapping("/login")
    @ResponseStatus(HttpStatus.OK)
    public ResultMap login(HttpServletRequest request,
                           @ApiParam(value = "手机号码", required = true)
                           @RequestParam(name = "mobile", value = "", required = true) String mobile,
                           @ApiParam(value = "登录方式", required = true)
                           @RequestParam(name = "loginType", value = "", required = true) int loginType,
                           @ApiParam(value = "区域号码", required = true)
                           @RequestParam(name = "countryCode", value = "", required = true) String countryCode,
                           @ApiParam(value = "密码", required = false)
                           @RequestParam(name = "password", value = "", required = false) String password,
                           @ApiParam(name = "验证码", required = false)
                           @RequestParam(name = "verifyCode", value = "", required = false) String verifyCode) {
        // 基础校验 验证码不为空说明使用的是验证码+图形验证码登录 密码不为空则使用密码登录
        String lang = request.getHeader("lang");
        // 判断手机号码
        boolean mobileRight = apiMobileCheckFacade.isMobileRight(countryCode, mobile);
        if (!mobileRight) {
            return ApiResultUtil.failResult(PublicCode.MULTI_102932.getI18nCode(), PublicCode.MULTI_102932.getMultiCode());
        }
        // 是否注册过
        Result<TwodfireMember> result = twodfireMemberService.queryInfoByMobile(mobile, countryCode);
        if (result.isSuccess() && result.getModel() == null) {
            return ApiResultUtil.failResult(WeixinMealCode.MULTI_WMI054.getI18nCode(), WeixinMealCode.MULTI_WMI054.getMultiCode()); // 未找到用户信息
        }
        // 拿到验证结果
        return cameraScanService.loginCheck(countryCode, mobile, password, verifyCode, lang, loginType);
    }


    @ApiOperation("注册")
    @PostMapping("/register")
    @ResponseStatus(HttpStatus.OK)
    public ResultMap registeredMember(HttpServletRequest request,
                                      @ApiParam(value = "手机号码", required = true)
                                      @RequestParam(name = "mobile", value = "", required = true) String mobile,
                                      @ApiParam(value = "区域号码", required = true)
                                      @RequestParam(name = "countryCode", value = "", required = true) String countryCode,
                                      @ApiParam(value = "密码", required = false)
                                      @RequestParam(name = "password", value = "", required = true) String password,
                                      @ApiParam(name = "验证码", required = false)
                                      @RequestParam(name = "verifyCode", value = "", required = true) String verifyCode) {
        // 基础校验 验证码不为空说明使用的是验证码+图形验证码登录 密码不为空则使用密码登录
        // 判断手机号码
        boolean mobileRight = apiMobileCheckFacade.isMobileRight(countryCode, mobile);
        if (!mobileRight) {
            return ApiResultUtil.failResult(PublicCode.MULTI_102932.getI18nCode(), PublicCode.MULTI_102932.getMultiCode());
        }
        // 是否注册过
        Result<TwodfireMember> result = twodfireMemberService.queryInfoByMobile(mobile, countryCode);
        if (ResultUtil.isModelNotNull(result)) {
            TwodfireMember twodfireMember = result.getModel();
            if (!StringUtils.isBlank(twodfireMember.getId()))
                return ApiResultUtil.failResult(WeixinMealCode.MULTI_WMI073.getI18nCode(), WeixinMealCode.MULTI_WMI073.getMultiCode());
        }
        String lang = request.getHeader("lang");
        // 返回调用oath登录
        return cameraScanService.registerAndLogin(mobile, countryCode, verifyCode, password, lang);

    }

    @ApiOperation(value = "获取国国家区号列表", response = ResultMap.class)
    @GetMapping("/country/list")
    @ResponseStatus(HttpStatus.OK)
    public ResultMap getCountryList(HttpServletRequest request,
                                    @ApiParam(value = "店铺id", required = true)
                                    @RequestParam(name = "entityId", value = "", required = true) String entityId) {
        String lang = request.getHeader("lang");
        return cameraScanService.countryLists(lang, entityId);
    }

    /**
     * 该接口不要再提供给前端使用
     * @param request
     * @param mobile
     * @param sourceType
     * @param countryCode
     * @return
     */
    @ApiOperation(value = "获取短信验证码", response = ResultMap.class)
    @GetMapping("/verify/code")
    @ResponseStatus(HttpStatus.OK)
    @Deprecated
    public ResultMap getVerifyCode(HttpServletRequest request, @ApiParam(value = "手机号", required = true)
    @RequestParam(name = "mobile", value = "", required = true) String mobile,
                                   @ApiParam(value = "来源 1-登录 2-注册", required = true)
                                   @RequestParam(name = "sourceType", value = "", required = true) Integer sourceType,
                                   @ApiParam(value = "区域号码", required = true)
                                   @RequestParam(name = "countryCode", value = "", required = true) String countryCode) {

        //获取请求头语言
        String lang = request.getHeader("lang");
        // 判断手机号码
        try {
            boolean mobileRight = apiMobileCheckFacade.isMobileRight(countryCode, mobile);
            if (!mobileRight) {
                return ApiResultUtil.failResult(PublicCode.MULTI_102932.getI18nCode(), PublicCode.MULTI_102932.getMultiCode());
            }
        } catch (Exception e) {
            return ApiResultUtil.failResult(WeixinMealCode.MULTI_WMI075.getI18nCode(), WeixinMealCode.MULTI_WMI075.getMultiCode());
        }
        // 是否注册过
        Result<TwodfireMember> result = twodfireMemberService.queryInfoByMobile(mobile, countryCode);
        // 注册-提示已经注册了
        if (sourceType == Constants.CameraScan.REGISTER) {
            if (ResultUtil.isModelNotNull(result)) {
                TwodfireMember twodfireMember = result.getModel();
                if (!StringUtils.isBlank(twodfireMember.getId())) {
                    return ApiResultUtil.failResult(WeixinMealCode.MULTI_WMI073.getI18nCode(), WeixinMealCode.MULTI_WMI073.getMultiCode());
                }
            }
        }
        // 登录 如果没有用户提示没用户
        if (sourceType == Constants.CameraScan.LOGIN) {
            if (result.isSuccess() && result.getModel() == null) {
                return ApiResultUtil.failResult(WeixinMealCode.MULTI_WMI054.getI18nCode(), WeixinMealCode.MULTI_WMI054.getMultiCode());
            }
        }

        //发送短信 + 缓存验证码
        boolean isSend = cameraScanService.checkMobileSendMsg(lang, countryCode, mobile);
        if (!isSend) {
            return ApiResultUtil.failResult(ErrorMessageEnum.MULTI_WMI245.getI18nCode(), ErrorMessageEnum.MULTI_WMI245.getMultiCode());
        }
        return ApiResultUtil.defaultResult();
    }

    @ApiOperation(value = "获取图形验证码", response = ResultMap.class)
    @GetMapping("/image/code")
    @ResponseStatus(HttpStatus.OK)
    public ResultMap getImageCode(HttpServletRequest request,
                                  @ApiParam(name = "图形验证码uuid", required = false)
                                  @RequestParam(name = "imageCodeUUID", value = "", required = false) String imageCodeUUID) {
        try {

            String graphicVerifyCode = CodeGeneratorUtils.getGraphicVerify(4);
            String imageStr = CodeGeneratorUtils.encodeBase64ImgCode(graphicVerifyCode);
            if (StringUtils.isBlank(imageCodeUUID)) {
                // 获取uuid
                imageCodeUUID = UuidUtil.getUUID();
            }
            // 替换缓存中的验证码 默认5分钟过期
            cacheService.set(imageCodeUUID, graphicVerifyCode, 5 * 60);
            // 生成对象返回
            ImageDown imageDown = new ImageDown(imageCodeUUID, imageStr);
            return ApiResultUtil.successResult(imageDown);
        } catch (Exception e) {
            LoggerUtil.error(WXLoggerFactory.BUSINESS_LOGGER, LogMarker.BUSINESS, "获取图片验证码异常", e);
        }

        return ApiResultUtil.failResult(ErrorMessageEnum.MULTI_WMI247.getI18nCode(), ErrorMessageEnum.MULTI_WMI247.getMultiCode());
    }

    @ApiOperation(value = "第三登陆方授权认证", response = ResultMap.class)
    @RequestMapping(value = "/thirdLogin", method = RequestMethod.GET)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public ResultMap abroadThirdPlatformLogin(HttpServletResponse response,
                                              @ApiParam(name = "平台类型", required = false)
                                              @RequestParam(name = "platformType", required = false) Integer platformType,
                                              @ApiParam(name = "key", required = false)
                                              @RequestParam(name = "key", required = false) String key
    ) throws IOException {

        OAuthThirdPageOAuthSignRequest oAuthThirdPageOAuthSignRequest = new OAuthThirdPageOAuthSignRequest();
        oAuthThirdPageOAuthSignRequest.setForeignType(ForeignThirdPlatform.getPlatformByType(platformType));
        oAuthThirdPageOAuthSignRequest.setKey(key);

        Result<String> result = authClientService.execute(oAuthThirdPageOAuthSignRequest);
        LoggerUtil.info(WXLoggerFactory.BUSINESS_LOGGER, LogMarker.BUSINESS, "请求第三方授权:", result.getModel());
        //使用前端跳转
        return ApiResultUtil.successResult(result.getModel());

    }


    @ApiOperation(value = "华为鸿蒙登陆获取短信验证码", response = ResultMap.class)
    @GetMapping("/huawei/verifyCode")
    @ResponseStatus(HttpStatus.OK)
    public ResultMap getHuaweiVerifyCode(
            @ApiParam(value = "手机号", required = true)
            @RequestParam(name = "mobile", value = "", required = true) String mobile,
            @ApiParam(value = "区域号码", required = true)
            @RequestParam(name = "countryCode", value = "", required = true) String countryCode) {
        // 判断手机号码
        try {
            boolean mobileRight = apiMobileCheckFacade.isMobileRight(countryCode, mobile);
            if (!mobileRight) {
                return ApiResultUtil.failResult(PublicCode.MULTI_102932.getI18nCode(), PublicCode.MULTI_102932.getMultiCode());
            }
            if (!cameraScanService.checkMobileSendMsg5Min(countryCode, mobile)) {
                return ApiResultUtil.failResult(WeixinMealCode.MULTI_WMI075.getI18nCode(), WeixinMealCode.MULTI_WMI075.getMultiCode());
            }

            //每个手机号每天最多发送验证码5次
            if (!cameraScanService.checkMobileSendMsgLimit(countryCode, mobile)) {
                return ApiResultUtil.failResult(ErrorMessageEnum.MULTI_WMI253.getI18nCode(), ErrorMessageEnum.MULTI_WMI253.getMultiCode());
            }
            //发送短信 + 缓存验证码
            boolean isSend = cameraScanService.checkMobileSendMsg(countryCode, mobile);
            if (!isSend) {
                return ApiResultUtil.failResult(ErrorMessageEnum.MULTI_WMI245.getI18nCode(), ErrorMessageEnum.MULTI_WMI245.getMultiCode());
            }
            cameraScanService.incrMobileSendMsgLimit(countryCode, mobile);
        } catch (Exception e) {
            LoggerUtil.error(WXLoggerFactory.BUSINESS_LOGGER, LoggerMarkers.BUSINESS, e);
            return ApiResultUtil.failResult(WeixinMealCode.MULTI_WMI075.getI18nCode(), WeixinMealCode.MULTI_WMI075.getMultiCode());
        }

        return ApiResultUtil.defaultResult();
    }


    @ApiOperation(value = "华为鸿蒙登陆", response = ResultMap.class)
    @RequestMapping(value = "/huaweiLogin")
    @ResponseStatus(HttpStatus.OK)
    public ResultMap huaweiLogin(@ApiParam(required = true, name = "手机号码")
                                 @RequestParam(required = true, name = "mobile", value = "") String mobile,
                                 @ApiParam(required = true, name = "国家编号")
                                 @RequestParam(required = true, name = "countryCode", value = "") String countryCode,
                                 @ApiParam(required = true, name = "短信验证码")
                                 @RequestParam(required = true, name = "smsCode", value = "") String smsCode) {
        if (StringUtils.isBlank(smsCode)) {
            return ApiResultUtil.failResult(WeixinMealCode.MULTI_WMI074.getI18nCode(), WeixinMealCode.MULTI_WMI074.getMultiCode());
        }
        if (StringUtils.isBlank(mobile) || StringUtils.isBlank(countryCode)) {
            return ApiResultUtil.failResult(PublicCode.MULTI_102932.getI18nCode(), PublicCode.MULTI_102932.getMultiCode());
        }
        boolean mobileRight = apiMobileCheckFacade.isMobileRight(countryCode, mobile);
        if (!mobileRight) {
            return ApiResultUtil.failResult(PublicCode.MULTI_102932.getI18nCode(), PublicCode.MULTI_102932.getMultiCode());
        }
        // 注册并登陆 1- 如果已经注册直接登陆 2-没有注册注册并登陆
        return cameraScanService.huaweiLogin(mobile, countryCode, smsCode);

    }


}
