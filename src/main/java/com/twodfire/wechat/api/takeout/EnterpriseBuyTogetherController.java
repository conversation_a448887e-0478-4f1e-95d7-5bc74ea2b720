package com.twodfire.wechat.api.takeout;

import com.dfire.consumer.i18n.code.PublicCode;
import com.dfire.consumer.util.annotation.Check;
import com.dfire.consumer.util.annotation.Validator;
import com.dfire.consumer.util.validator.NotNull;
import com.dfire.soa.consumer.param.CartQueryRequest;
import com.dfire.soa.consumer.service.cart.ITakeoutCartService;
import com.dfire.soa.consumer.takeout.dto.enterprise.EnterpriseBuyTogetherInfoDTO;
import com.dfire.soa.consumer.takeout.user.dto.enterprise.BuyTogetherDTO;
import com.dfire.soa.consumer.takeout.user.service.enterprise.IEnterpriseBuyTogetherInfoService;
import com.dfire.soa.consumer.takeout.user.service.enterprise.IEnterpriseDeliveryService;
import com.dfire.soa.consumer.takeout.user.vo.enterprise.*;
import com.dfire.soa.consumer.vo.DinningTableVo;
import com.dfire.soa.oauth.common.domain.SessionUserInfo;
import com.dfire.soa.shop.bo.Shop;
import com.dfire.soa.shop.service.IShopClientService;
import com.twodfire.share.result.Result;
import com.twodfire.share.result.ResultMap;
import com.twodfire.share.util.ApiResultUtil;
import com.twodfire.share.util.ResultUtil;
import com.twodfire.wechat.api.BaseController;
import com.twodfire.wechat.props.EnterpriseTakeoutProperties;
import com.twodfire.wechat.vo.takeout.enterprise.EnterpriseShareInfoVo;
import com.wordnik.swagger.annotations.ApiOperation;
import com.wordnik.swagger.annotations.ApiParam;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 项目名称：online-wechat
 * 类描述：企业外卖拼单业务
 * 创建时间：2018年11月09日 09时41分
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping(value = "enterprise/takeout/buytogether/v1")
public class EnterpriseBuyTogetherController extends BaseController {

    @Resource
    private IEnterpriseBuyTogetherInfoService enterpriseBuyTogetherInfoService;
    @Resource
    private IEnterpriseDeliveryService enterpriseDeliveryService;
    @Resource
    private ITakeoutCartService consumerTakeoutCartService;
    @Resource
    private EnterpriseTakeoutProperties enterpriseTakeoutProperties;
    @Resource
    private IShopClientService shopClientService;

    @ApiOperation(value = "获取送达时间", response = ResultMap.class)
    @RequestMapping(value = "/get_delivery_time", method = RequestMethod.GET)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    @Validator({@Check(name = "enterpriseId", adapter = NotNull.class, message = "enterpriseId不能为空", errorCode = "0"),
                @Check(name = "entityId", adapter = NotNull.class, message = "entityId不能为空", errorCode = "0")})
    public ResultMap getShopList(final HttpServletRequest request,
                                 @ApiParam(name = "企业id", required = true)
                                 @RequestParam(value = "enterpriseId") final String enterpriseId,
                                 @ApiParam(name = "商户id", required = true)
                                 @RequestParam(value = "entityId") final String entityId) throws Exception {

        if (enterpriseId == null) {
            return ApiResultUtil.failResult(PublicCode.MULTI_102951.getErrCode(), "enterpriseId参数为空");
        }

        final SessionUserInfo sessionUserInfo = sessionHelper.getSessionUserInfo(request);
        String customerId = sessionUserInfo.getCustomerId();

        // 获取送达时间
        Result<EnterpriseDeliveryTimeVO> timeResult = enterpriseDeliveryService.getBuyTogetherDeliveryTime(enterpriseId,entityId,customerId);
        if (!ResultUtil.isResultSuccess(timeResult)){
            return ApiResultUtil.failResult(timeResult.getResultCode(),timeResult.getMessage());
        }
        return ApiResultUtil.successResult(timeResult.getModel());

    }

    @ApiOperation(value = "修改送达时间验证", response = ResultMap.class)
    @RequestMapping(value = "/modify_delivery_time", method = RequestMethod.GET)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    @Validator({@Check(name = "enterpriseId", adapter = NotNull.class, message = "enterpriseId不能为空", errorCode = "0"),
                @Check(name = "entityId", adapter = NotNull.class, message = "entityId不能为空", errorCode = "0"),
                @Check(name = "deliveryTime", adapter = NotNull.class, message = "deliveryTime不能为空", errorCode = "0")})
    public ResultMap getShopList(final HttpServletRequest request,
                                 @ApiParam(name = "企业id", required = true)
                                 @RequestParam(value = "enterpriseId") final String enterpriseId,
                                 @ApiParam(name = "商户id", required = true)
                                 @RequestParam(value = "entityId") final String entityId,
                                 @ApiParam(name = "更新后送达时间", required = true)
                                 @RequestParam(value = "deliveryTime") final Long deliveryTime) throws Exception {

        EnterpriseBuyTogetherInfoDTO dto = new EnterpriseBuyTogetherInfoDTO();
        dto.setEnterpriseId(enterpriseId);
        dto.setEntityId(entityId);
        dto.setDeliveryTime(deliveryTime);

        final SessionUserInfo sessionUserInfo = sessionHelper.getSessionUserInfo(request);
        String customerId = sessionUserInfo.getCustomerId();
        dto.setCustomerId(customerId);
        Result<CheckResultVO> result = enterpriseBuyTogetherInfoService.updateDeliveryTime(dto);

        if (!ResultUtil.isResultSuccess(result)){
            return ApiResultUtil.failResult(result.getResultCode(),result.getMessage());
        }
        return ApiResultUtil.successResult(result.getModel());
    }


    @ApiOperation(value = "是否可以发起拼单", response = ResultMap.class)
    @RequestMapping(value = "/can_launch", method = {RequestMethod.POST,RequestMethod.GET})
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public ResultMap canLaunch(@RequestParam(value = "company_id") String companyId,
                            @RequestParam(value = "entity_id") String entityId,
                            HttpServletRequest request) {
        SessionUserInfo sessionUserInfo = sessionHelper.getSessionUserInfo(request);
        String customerId = sessionUserInfo.getCustomerId();

        EnterpriseBuyTogetherInfoDTO dto = new EnterpriseBuyTogetherInfoDTO();
        dto.setCustomerId(customerId);
        dto.setEntityId(entityId);
        dto.setEnterpriseId(companyId);
        dto.setLaunchTime(System.currentTimeMillis());
        Result<BuyTogetherCanLaunchVO> result = enterpriseBuyTogetherInfoService.canLaunch(dto);
        if (!ResultUtil.isResultSuccess(result)){
            return ApiResultUtil.failResult(result.getResultCode(), result.getMessage());
        }
        return ApiResultUtil.successResult(result.getModel());
    }

    @ApiOperation(value = "发起拼单", response = ResultMap.class)
    @RequestMapping(value = "/launch", method = RequestMethod.POST)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public ResultMap launch(@RequestParam(value = "company_id") String companyId,
                            @RequestParam(value = "entity_id") String entityId,
                            @RequestParam(value = "delivery_time") Long deliveryTime,
                            @RequestParam(value = "company_takeout") String companyTakeout,
                            HttpServletRequest request) {
        SessionUserInfo sessionUserInfo = sessionHelper.getSessionUserInfo(request);
        String customerId = sessionUserInfo.getCustomerId();

        BuyTogetherDTO dto = new BuyTogetherDTO();
        dto.setCustomerId(customerId);
        dto.setEntityId(entityId);
        dto.setEnterpriseId(companyId);
        dto.setLaunchTime(System.currentTimeMillis());
        dto.setDeliveryTime(deliveryTime);
        dto.setCompanyTakeout(companyTakeout);

        Result<BuyTogetherCanLaunchVO> result = enterpriseBuyTogetherInfoService.launch(dto);
        if (!ResultUtil.isResultSuccess(result)){
            return ApiResultUtil.failResult(result.getResultCode(),result.getMessage());
        }
        return ApiResultUtil.successResult(result.getModel());
    }

    @ApiOperation(value = "拼单页", response = ResultMap.class)
    @RequestMapping(value = "/home", method = {RequestMethod.POST,RequestMethod.GET})
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public ResultMap home(@RequestParam(value = "order_id") String orderId,
                            HttpServletRequest request) {
        SessionUserInfo sessionUserInfo = sessionHelper.getSessionUserInfo(request);
        String customerId = sessionUserInfo.getCustomerId();

        Result<BuyTogetherHomeVO> result = enterpriseBuyTogetherInfoService.getHomeInfo(Long.valueOf(orderId));
        if (!ResultUtil.isResultSuccess(result)){
            return ApiResultUtil.failResult(result.getResultCode(),result.getMessage());
        }
        BuyTogetherHomeVO homeVO = result.getModel();
        homeVO.setCurrentMemberId(customerId);

        CartQueryRequest queryRequest = new CartQueryRequest(homeVO.getEntityId(), homeVO.getCustomerId());
        Result<DinningTableVo> cartResult = consumerTakeoutCartService.queryCart(queryRequest);
        if (!ResultUtil.isResultSuccess(cartResult)){
            return ApiResultUtil.failResult(cartResult.getResultCode(),cartResult.getMessage());
        }
        homeVO.setCartsData(cartResult.getModel());

        return ApiResultUtil.successResult(homeVO);
    }

    @ApiOperation(value = "删除参与人", response = ResultMap.class)
    @RequestMapping(value = "/delete_member", method = RequestMethod.POST)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public ResultMap deleteMember(@RequestParam(value = "order_id") String orderId,
                            @RequestParam(value = "customer_id") String targetCusotmerId,
                            HttpServletRequest request) {
        SessionUserInfo sessionUserInfo = sessionHelper.getSessionUserInfo(request);
        String customerId = sessionUserInfo.getCustomerId();

        Result<Boolean> result = enterpriseBuyTogetherInfoService.removeJoiner(Long.valueOf(orderId), targetCusotmerId);

        if (!ResultUtil.isResultSuccess(result)){
            return ApiResultUtil.failResult(result.getResultCode(),result.getMessage());
        }
        return ApiResultUtil.successResult(result.getModel());
    }


    @ApiOperation(value = "加入拼单", response = ResultMap.class)
    @RequestMapping(value = "/join", method = RequestMethod.POST)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public ResultMap join(@RequestParam(value = "order_id") String orderId,
                            HttpServletRequest request) {
        SessionUserInfo sessionUserInfo = sessionHelper.getSessionUserInfo(request);
        String customerId = sessionUserInfo.getCustomerId();

        Result<BuyTogetherAddJoinerVO> result = enterpriseBuyTogetherInfoService.addJoiner(Long.valueOf(orderId), customerId);
        if (!ResultUtil.isResultSuccess(result)){
            return ApiResultUtil.failResult(result.getResultCode(),result.getMessage());
        }
        return ApiResultUtil.successResult(result.getModel());
    }


    @ApiOperation(value = "删除拼单", response = ResultMap.class)
    @RequestMapping(value = "/cancel_order", method = RequestMethod.POST)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public ResultMap cancelOrder(@RequestParam(value = "order_id") String orderId,
                            HttpServletRequest request) {
        SessionUserInfo sessionUserInfo = sessionHelper.getSessionUserInfo(request);
        String customerId = sessionUserInfo.getCustomerId();

        Result<Boolean> result = enterpriseBuyTogetherInfoService.cancelOrder(Long.valueOf(orderId), customerId);
        if (!ResultUtil.isResultSuccess(result)){
            return ApiResultUtil.failResult(result.getResultCode(),result.getMessage());
        }
        return ApiResultUtil.successResult(result.getModel());
    }


    @ApiOperation(value = "拼单页", response = ResultMap.class)
    @RequestMapping(value = "/get_share_info", method = {RequestMethod.GET})
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public ResultMap getShareInfo(@RequestParam(value = "order_id") String orderId,
                                  @RequestParam(value = "entity_id") String entityId) {
        EnterpriseShareInfoVo shareInfoVo = new EnterpriseShareInfoVo();

        String shareUrl = enterpriseTakeoutProperties.getBuytogetherShareUrl()
                          +"/"+entityId+"/"+orderId;
        shareInfoVo.setShareUrl(shareUrl);

        // 店铺基础信息
        Result<Shop> result = shopClientService.getShopDetial(entityId);
        if (ResultUtil.isModelNotNull(result)) {
            String shopName = result.getModel().getName();
            String logoImgUrl = result.getModel().getLogoImgUrl();
            if (logoImgUrl != null && logoImgUrl.trim().length() > 0) {
                logoImgUrl = logoImgUrl.startsWith("http://") ? logoImgUrl : "http://" + logoImgUrl;
            }

            shareInfoVo.setShopName(shopName);
            shareInfoVo.setShopLogo(logoImgUrl);
        }

        return ApiResultUtil.successResult(shareInfoVo);
    }



    @ApiOperation(value = "送达时间校验", response = ResultMap.class)
    @RequestMapping(value = "/check_delivery_time", method = {RequestMethod.POST})
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public ResultMap checkDeliveryTime(@RequestParam(value = "delivery_time") Long deliveryTime) {
        if (deliveryTime <= System.currentTimeMillis()) {
            return ApiResultUtil.failResult("0","送达时间需小于当前时间");

        }
        return ApiResultUtil.successResult(true);
    }


}
