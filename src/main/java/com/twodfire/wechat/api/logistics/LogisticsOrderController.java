/*
 * Copyright (C) 2009-2016 Hangzhou 2Dfire Technology Co., Ltd. All rights reserved
 */
package com.twodfire.wechat.api.logistics;

import javax.annotation.Resource;

import com.dfire.soa.logistics.express.dto.order.LogisticsOrderDTO;
import com.dfire.soa.logistics.express.request.QueryTracesByOrderIdRequest;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.dfire.consumer.util.LoggerUtil;
import com.dfire.consumer.util.exception.DubboBizException;
import com.dfire.soa.lp.facade.LogisticsOrderFacade;
import com.twodfire.share.result.Result;
import com.twodfire.share.result.ResultMap;
import com.twodfire.share.util.ApiResultUtil;
import com.twodfire.wechat.api.BaseController;
import com.twodfire.wechat.common.logger.WXLoggerFactory;
import com.twodfire.wechat.utils.ResultMapUtil;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiOperation;
import com.wordnik.swagger.annotations.ApiParam;

/**
 * LogisticsOrderController
 *
 * <AUTHOR>
 * @since 2018-06-02
 */
@Api(value = "LogisticsOrder(物流相关)")
@Controller
@RequestMapping("/lg/order")
public class LogisticsOrderController extends BaseController {
	@Resource
	LogisticsOrderFacade logisticsOrderFacade;

	@RequestMapping(value = "/queryTraces", method = { RequestMethod.GET })
	@ResponseBody
	@ApiOperation(value = "查询订单的物流轨迹", response = ResultMap.class)
	public ResultMap cartRechargeAgreement(@ApiParam(value = "订单号", required = false) @RequestParam(value = "order_id", required = true, defaultValue = "") String orderId, @ApiParam(value = "店铺号", required = false) @RequestParam(value = "entity_id", required = true, defaultValue = "") String entityId) {
		try {
			QueryTracesByOrderIdRequest request =new QueryTracesByOrderIdRequest();
			request.setEntityId(entityId);
			request.setOrderId(orderId);
			Result<LogisticsOrderDTO> tracesResult = logisticsOrderFacade.queryTracesByOrderId(request);
			if (tracesResult.isSuccess()) {
				return ResultMapUtil.retResultMap(tracesResult);
			} else {
				this.printFailureLog(orderId, entityId, tracesResult);
				return ApiResultUtil.failResult(tracesResult.getResultCode(), tracesResult.getMessage());
			}
		} catch (DubboBizException e) {
			this.printFailureLog(orderId, entityId, e);
			return ApiResultUtil.failResult(e.getCode(), e.getMessage());
		} catch (Exception e) {
			this.printFailureLog(orderId, entityId, e);
			return ApiResultUtil.failResult("0","服务忙，请稍后重试");
		}
	}

	// printFailureLog methods
	private void printFailureLog(String orderId, String entityId, Result<LogisticsOrderDTO> tracesResult) {
		LoggerUtil.error(WXLoggerFactory.RETAIL_LOGGER, "queryTracesByOrderId failed. resultCode={}, returnMsg={},entity_id={},order_id={}", tracesResult.getResultCode(), tracesResult.getMessage(), entityId, orderId);
	}

	private void printFailureLog(String orderId, String entityId, DubboBizException e) {
		LoggerUtil.error(WXLoggerFactory.RETAIL_LOGGER, e, "queryTracesByOrderId failed. resultCode={}, returnMsg={},entity_id={},order_id={}", e.getCode(), e.getMessage(), entityId, orderId);
	}

	private void printFailureLog(String orderId, String entityId, Exception e) {
		LoggerUtil.error(WXLoggerFactory.RETAIL_LOGGER, e, "queryTracesByOrderId failed. errorMsg={},entity_id={},order_id={}", e.getMessage(), entityId, orderId);
	}
}
