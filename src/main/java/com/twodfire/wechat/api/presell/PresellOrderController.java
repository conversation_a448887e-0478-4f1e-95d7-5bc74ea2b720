package com.twodfire.wechat.api.presell;


import com.dfire.consumer.i18n.code.PublicCode;
import com.dfire.soa.oauth.common.domain.SessionUserInfo;
import com.dfire.soa.order.constant.CommonConstant;
import com.dfire.tp.client.order.request.SubmitPresellInstanceOrderRequest;
import com.dfire.tp.client.order.request.SubmitPresellTakeoutInstanceOrderRequest;
import com.dfire.tp.client.order.response.SubmitPresellInstanceOrderResponse;
import com.dfire.tp.client.order.response.SubmitPresellTakeoutInstanceOrderResponse;
import com.dfire.tp.client.service.ITradePlatformService;
import com.twodfire.share.result.Result;
import com.twodfire.share.result.ResultMap;
import com.twodfire.share.util.ApiResultUtil;
import com.twodfire.wechat.api.BaseController;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR> lihao
 * Date: 2016/10/12
 * Time: 2018/11/27 14:37
 */
@Controller
@RequestMapping("/presell/order/")
@SuppressWarnings("unchecked")
public class PresellOrderController extends BaseController {

    @Resource
    private ITradePlatformService tradePlatformService;
    // @Resource
    // private IPresellOrderFacade presellOrderFacade;

    /**
     * 预售堂食加菜下单
     *
     * @param request        请求对象
     * @param seatCode       座位码
     * @param waitingOrderId 预售单id
     * @param entityId       店铺id
     * @param cartTime       购物车时间
     * @return ResultMap
     */
    @RequestMapping(value = "v1/for_here/submit_order", method = RequestMethod.POST)
    @ResponseBody
    public ResultMap submitForHere(HttpServletRequest request,
                                   @RequestParam(value = "seat_code", defaultValue = "") String seatCode,
                                   @RequestParam(value = "waiting_order_id", defaultValue = "") String waitingOrderId,
                                   @RequestParam(value = "entity_id", defaultValue = "") String entityId,
                                   @RequestParam(value = "cart_time") Long cartTime) {
        SessionUserInfo sessionUserInfo = sessionHelper.getSessionUserInfo(request);
        SubmitPresellInstanceOrderRequest submitPresellInstanceOrderRequest = new SubmitPresellInstanceOrderRequest();
        submitPresellInstanceOrderRequest.setSeatCode(seatCode);
        submitPresellInstanceOrderRequest.setWaitingOrderId(waitingOrderId);
        submitPresellInstanceOrderRequest.setEntityId(entityId);
        submitPresellInstanceOrderRequest.setCartTime(cartTime);
        submitPresellInstanceOrderRequest.setCustomerRegisterId(sessionUserInfo.getCustomerId());
        Result<SubmitPresellInstanceOrderResponse> result = tradePlatformService.submitPresellInstanceOrder(submitPresellInstanceOrderRequest);
        if (result.isSuccess()) {
            return ApiResultUtil.successResult(Boolean.TRUE);
        } else {
            return ApiResultUtil.failResult(result.getResultCode(), result.getMessage());
        }
    }

    /**
     * 预售外卖加菜下单
     *
     * @param request     请求对象
     * @param entityId    店铺id
     * @param peopleCount 人数
     * @param orderFee    订单金额
     * @param memo        备注
     * @return SubmitPresellTakeoutInstanceOrderResponse
     */
    @RequestMapping(value = "v1/take_out/submit_order", method = RequestMethod.POST)
    @ResponseBody
    public ResultMap submitForTakeout(HttpServletRequest request,
                                      @RequestParam(value = "entity_id") String entityId,
                                      @RequestParam(value = "waiting_order_id") String waitingOrderId,
                                      @RequestParam(value = "seat_code") String seatCode,
                                      @RequestParam(value = "cart_time") Long cartTime,
                                      @RequestParam(value = "people_count") Integer peopleCount,
                                      @RequestParam(value = "order_fee") Integer orderFee,
                                      @RequestParam(value = "memo", required = false, defaultValue = "") String memo,
                                      @RequestParam(value = "address_id") String addressId,
                                      @RequestParam(value = "delivery_type") Short deliveryType) {
        SessionUserInfo sessionUserInfo = sessionHelper.getSessionUserInfo(request);
        SubmitPresellTakeoutInstanceOrderRequest takeoutSubmitRequest = new SubmitPresellTakeoutInstanceOrderRequest();
        takeoutSubmitRequest.setEntityId(entityId);
        takeoutSubmitRequest.setWaitingOrderId(waitingOrderId);
        takeoutSubmitRequest.setSeatCode(seatCode);
        takeoutSubmitRequest.setCartTime(cartTime);
        takeoutSubmitRequest.setPeopleCount(peopleCount);
        takeoutSubmitRequest.setMemo(memo);
        takeoutSubmitRequest.setCustomerRegisterId(sessionUserInfo.getCustomerId());
        takeoutSubmitRequest.setClientFrom(CommonConstant.ORDER_FROM_PRESELL_TAKEOUT.intValue());
        takeoutSubmitRequest.setOrderFrom(CommonConstant.ORDER_FROM_PRESELL_TAKEOUT.intValue());
        takeoutSubmitRequest.setOrderFee(orderFee);
        takeoutSubmitRequest.setAddressId(addressId);
        takeoutSubmitRequest.setDeliveryType(deliveryType);
        takeoutSubmitRequest.setExpressType(CommonConstant.EXPRESS_TYPE_SEND);
        takeoutSubmitRequest.setUserName(sessionUserInfo.getNickname());
        takeoutSubmitRequest.setMobile(sessionUserInfo.getMobile());
        Result<SubmitPresellTakeoutInstanceOrderResponse> result = tradePlatformService.execute(takeoutSubmitRequest);
        if (result.isSuccess()) {
            return ApiResultUtil.successResult(Boolean.TRUE);
        } else {
            return ApiResultUtil.failResult(result.getResultCode(), result.getMessage());
        }
    }

    /**
     * 获取订单列表
     *
     * @param httpServletRequest 请求对象
     * @param orderType          订单类型
     * @param cursorMark         游标
     * @param limit              查询条数
     * @return ResultMap 订单列表
     */
    @RequestMapping("v1/get_order_list")
    @ResponseBody
    public ResultMap getOrderList(HttpServletRequest httpServletRequest,
                                  @RequestParam(value = "order_type", required = false, defaultValue = "0") Integer orderType,
                                  @RequestParam(value = "cursor_mark", required = false) String cursorMark,
                                  @RequestParam(value = "limit", required = false) Integer limit) {
        return ApiResultUtil.failResult(PublicCode.MULTI_102974.getErrCode(), PublicCode.MULTI_102974.getMessage());
        // if (null == cursorMark || StringUtils.isEmpty(cursorMark)) {
        //     cursorMark = "*";
        // }
        // if (null == limit) {
        //     limit = Constants.Http.PAGE_SIZE;
        // }
        // SessionUserInfo sessionUserInfo = sessionHelper.getSessionUserInfo(httpServletRequest);
        // OrderListDTO orderListQuery = new OrderListDTO();
        // orderListQuery.setOrderType(orderType);
        // orderListQuery.setCursorMark(cursorMark);
        // orderListQuery.setLimit(limit);
        // orderListQuery.setCustomerRegisterId(sessionUserInfo.getCustomerId());
        // Result<PresellOrderListVO> result = presellOrderFacade.queryOrderList(orderListQuery);
        // if (ResultUtil.isResultSuccess(result)) {
        //     return ApiResultUtil.successResult(result.getModel());
        // } else {
        //     return ApiResultUtil.failResult(result.getResultCode(), result.getMessage());
        // }
    }

    /**
     * 获取堂食预售单详情
     *
     * @param entityId 店铺Id
     * @param orderId  订单Id
     * @return ResultMap
     */
    @RequestMapping("v1/for_here/get_order_detail")
    @ResponseBody
    public ResultMap getForHereOrderDetail(@RequestParam(value = "entity_id") String entityId,
                                           @RequestParam(value = "order_id") String orderId) {
        return ApiResultUtil.failResult(PublicCode.MULTI_102974.getErrCode(), PublicCode.MULTI_102974.getMessage());
        // Result<PresellOrderDetailVO> result = presellOrderFacade.queryOrderDetail(entityId, orderId);
        // if (ResultUtil.isModelNotNull(result)) {
        //     return ApiResultUtil.successResult(result.getModel());
        // } else {
        //     return ApiResultUtil.failResult(result.getResultCode(), result.getMessage());
        // }
    }

    /**
     * getPresellOrderDetail 获取外送预售单详情
     *
     * @param entityId 店铺Id
     * @param orderId  订单Id
     * @return ResultMap
     */
    @RequestMapping("v1/takeout/get_order_detail")
    @ResponseBody
    public ResultMap getTakeoutOrderDetail(@RequestParam(value = "entity_id") String entityId,
                                           @RequestParam(value = "order_id") String orderId) {
        return ApiResultUtil.failResult(PublicCode.MULTI_102974.getErrCode(), PublicCode.MULTI_102974.getMessage());
        // Result<PresellTakeoutOrderDetailVO> result = presellOrderFacade.queryTakeoutOrderDetail(entityId, orderId);
        // if (ResultUtil.isModelNotNull(result)) {
        //     return ApiResultUtil.successResult(result.getModel());
        // } else {
        //     return ApiResultUtil.failResult(result.getResultCode(), result.getMessage());
        // }
    }
}
