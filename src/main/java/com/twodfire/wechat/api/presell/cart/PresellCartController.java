/*
 * Copyright (C) 2009-2018 Hangzhou FanDianEr Technology Co., Ltd. All rights reserved
 */
package com.twodfire.wechat.api.presell.cart;

import com.alibaba.fastjson.JSONArray;
import com.dfire.consumer.i18n.code.PublicCode;
import com.dfire.soa.consumer.param.CartItem;
import com.dfire.soa.oauth.common.domain.SessionUserInfo;
import com.twodfire.share.result.Result;
import com.twodfire.share.result.ResultMap;
import com.twodfire.share.util.ApiResultUtil;
import com.twodfire.share.util.ResultUtil;
import com.twodfire.wechat.api.BaseController;
import com.twodfire.wechat.enums.PresellCartTypeEnum;
import com.twodfire.wechat.facade.PresellClientCartFacade;
import com.twodfire.wechat.utils.ResultMapUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * PreTakeOutController
 * 预售堂食购物车 api
 *
 * <AUTHOR>
 * @since 2018-11-28
 */
@RestController
@RequestMapping(value = "/presell/carts")
@SuppressWarnings("unchecked")
public class PresellCartController extends BaseController {
    @Resource
    PresellClientCartFacade presellClientCartFacade;
    /**
     * 菜品最大数量
     */
    private static final Double ITEM_MAX_NUM = 99d;
    /**
     * 菜品最小数量
     */
    private static final Double ITEM_MIN_NUM = 1d;

    /**
     * 创建堂食购物车
     *
     * @param request  request
     * @param entityId 店铺 id
     * @param seatCode 桌位号
     * @return true/false
     */
    @RequestMapping(value = "/v1/create", method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.OK)
    public ResultMap createOwnCart(HttpServletRequest request,
                                   @RequestParam(value = "entity_id") String entityId,
                                   @RequestParam(value = "seat_code", defaultValue = "", required = false) String seatCode) {
        return ApiResultUtil.failResult(PublicCode.MULTI_102974.getErrCode(), PublicCode.MULTI_102974.getMessage());
        // JoinTableDTO joinTableDTO = new JoinTableDTO(entityId, seatCode, getCustomerId(request));
        // joinTableDTO.setBusinessScenario(PresellCartTypeEnum.PRESELL.getType());
        // Result result = presellClientCartFacade.joinTable(joinTableDTO);
        // if (!result.isSuccess()) {
        //     return ApiResultUtil.failResult(result.getResultCode(), result.getMessage());
        // }
        // return ApiResultUtil.successResult(PublicCode.MULTI_101549.getMultiCode());
    }

    /**
     * 获取堂食购物车数据
     *
     * @param entityId 店铺ID
     * @param seatCode 桌位号
     * @return dinningTableVO
     */
    @RequestMapping(value = "/v1/list", method = RequestMethod.GET)
    @ResponseStatus(HttpStatus.OK)
    public ResultMap getCartList(HttpServletRequest request,
                                 @RequestParam(value = "entity_id") String entityId,
                                 @RequestParam(value = "seat_code", defaultValue = "", required = false) String seatCode) {
        return ApiResultUtil.failResult(PublicCode.MULTI_102974.getErrCode(), PublicCode.MULTI_102974.getMessage());
        // QueryCartDTO queryCartDTO = new QueryCartDTO(entityId, seatCode, getCustomerId(request));
        // queryCartDTO.setBusinessScenario(PresellCartTypeEnum.PRESELL.getType());
        // Result result = presellClientCartFacade.queryCart(queryCartDTO);
        // if (!result.isSuccess()) {
        //     return ApiResultUtil.failResult(result.getResultCode(), result.getMessage());
        // } else {
        //     return ApiResultUtil.successResult(result.getModel());
        // }
    }

    /**
     * 修改堂食购物车商品
     *
     * @param request  request
     * @param cartItem 菜信息
     * @param entityId 店铺 id
     * @param seatCode 桌位号
     * @param source   来源
     * @return dinningTableVO
     */
    @RequestMapping(value = "/v1/async_modify", method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.OK)
    public ResultMap modifyCart(HttpServletRequest request,
                                @RequestBody CartItem cartItem,
                                @RequestParam(value = "entity_id", defaultValue = "") String entityId,
                                @RequestParam(value = "seat_code", defaultValue = "", required = false) String seatCode,
                                @RequestParam(value = "source", defaultValue = "", required = false) String source) {
        return ApiResultUtil.failResult(PublicCode.MULTI_102974.getErrCode(), PublicCode.MULTI_102974.getMessage());
        // if (null == cartItem.getNum() || cartItem.getNum() < 0) {
        //     cartItem.setNum(ITEM_MIN_NUM);
        // }
        // // 最大菜单数量不能大于99
        // if (cartItem.getNum() > ITEM_MAX_NUM) {
        //     cartItem.setNum(ITEM_MAX_NUM);
        // }
        // String customerId = getCustomerId(request);
        // ModifyCartItemsDTO modifyCartItemsDTO = new ModifyCartItemsDTO(entityId, seatCode, customerId);
        // modifyCartItemsDTO.setSource(source);
        // modifyCartItemsDTO.setBusinessScenario(PresellCartTypeEnum.PRESELL.getType());
        // Result result = presellClientCartFacade.modifyCartItems(modifyCartItemsDTO, cartItem);
        // if (result.isSuccess()) {
        //     return ApiResultUtil.successResult(result.getModel());
        // }
        // return ApiResultUtil.failResult(result.getResultCode(), result.getMessage());
    }

    /**
     * 判断堂食购物车是否发生变化
     *
     * @param request  request
     * @param entityId 店铺 id
     * @param seatCode 桌位号
     * @param cartTime 购物车时间戳
     * @return 0/1
     */
    @RequestMapping(value = "/v1/check_change", method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.OK)
    public ResultMap checkCartIsChange(HttpServletRequest request,
                                       @RequestParam(value = "entity_id") String entityId,
                                       @RequestParam(value = "seat_code") String seatCode,
                                       @RequestParam(value = "cart_time") String cartTime){
        return ApiResultUtil.failResult(PublicCode.MULTI_102974.getErrCode(), PublicCode.MULTI_102974.getMessage());
        // VerifyCartDTO verifyCartDTO = new VerifyCartDTO(entityId, seatCode, getCustomerId(request));
        // Long paramTime = StringUtils.isBlank(cartTime) ? 0L : Long.parseLong(cartTime);
        // verifyCartDTO.setCartTime(paramTime);
        // verifyCartDTO.setBusinessScenario(PresellCartTypeEnum.PRESELL.getType());
        // Result result = presellClientCartFacade.verifyCart(verifyCartDTO);
        // return ResultMapUtil.retResultMap(result);
    }

    /**
     * 清空堂食购物车商品
     *
     * @param entityId 店铺ID
     * @param seatCode 桌位号
     * @return 0/1
     */
    @RequestMapping(value = "/v1/clear", method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.OK)
    public ResultMap clearCart(HttpServletRequest request,
                               @RequestParam(value = "entity_id", defaultValue = "") String entityId,
                               @RequestParam(value = "seat_code", defaultValue = "", required = false) String seatCode,
                               @RequestParam(value = "source", defaultValue = "", required = false) String source) {
        return ApiResultUtil.failResult(PublicCode.MULTI_102974.getErrCode(), PublicCode.MULTI_102974.getMessage());
        // ClearCartDTO clearCartDTO = new ClearCartDTO(entityId, seatCode, getCustomerId(request));
        // clearCartDTO.setSource(source);
        // clearCartDTO.setClearOneCartExcludeForceMenu(true);
        // clearCartDTO.setBusinessScenario(PresellCartTypeEnum.PRESELL.getType());
        // Result result = presellClientCartFacade.clearCart(clearCartDTO);
        // if (result.isSuccess()) {
        //     return ApiResultUtil.successResult(result.getModel());
        // } else {
        //     return ApiResultUtil.failResult(result.getResultCode(), result.getMessage());
        // }
    }

    /**
     * 修改堂食购物车人数和备注信息
     *
     * @param entityId 店铺ID
     * @param seatCode 桌位号
     * @param people   人数
     * @param memo     备注信息
     * @param modifyType 修改类型（用户修改）
     * @return CommonCartVO
     */
    @RequestMapping(value = "/v1/modify_people_memo", method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.OK)
    public ResultMap modifyPeopleAndMemo(HttpServletRequest request,
                                         @RequestParam(value = "entity_id") String entityId,
                                         @RequestParam(value = "seat_code") String seatCode,
                                         @RequestParam(value = "people", defaultValue = "1", required = false) int people,
                                         @RequestParam(value = "memo", defaultValue = "", required = false) String memo,
                                         @RequestParam(value = "memo_labels", defaultValue = "", required = false) String memoLabels,
                                         @RequestParam(value = "modify_type", required = false) Integer modifyType){
        return ApiResultUtil.failResult(PublicCode.MULTI_102974.getErrCode(), PublicCode.MULTI_102974.getMessage());
        // ModifyCartCommentInfoDTO modifyCartCommentInfoDTO = new ModifyCartCommentInfoDTO(entityId, seatCode, getCustomerId(request));
        // List<String> memoLabelList = new ArrayList<>();
        // if (org.apache.commons.lang3.StringUtils.isNotBlank(memoLabels)) {
        //     memoLabelList = JSONArray.parseArray(memoLabels, String.class);
        // }
        // modifyCartCommentInfoDTO.setMemo(memo);
        // modifyCartCommentInfoDTO.setMemoLables(memoLabelList);
        // modifyCartCommentInfoDTO.setPeople(people);
        // modifyCartCommentInfoDTO.setBusinessScenario(PresellCartTypeEnum.PRESELL.getType());
        // if (null != modifyType) {
        //     modifyCartCommentInfoDTO.setModifyType(modifyType);
        // }
        // Result result = presellClientCartFacade.modifyCartCommentInfo(modifyCartCommentInfoDTO);
        // if (!ResultUtil.isResultSuccess(result)) {
        //     return ApiResultUtil.failResult("0","修改购物车人数与备注信息失败");
        // }
        // return ApiResultUtil.successResult(result.getModel());
    }

    /**
     * 判断 H5 堂食购物车沽清菜
     *
     * @param entityId  店铺id
     * @param timeStamp 当前时间
     * @return 沽清菜信息
     */
    @RequestMapping(value = "/v1/is_food_expired", method = RequestMethod.GET)
    @ResponseBody
    public ResultMap getExpiredGoods(HttpServletRequest request,
                                     @RequestParam(value = "entity_id") String entityId,
                                     @RequestParam(value = "time_stamp") Long timeStamp) {
        return ApiResultUtil.failResult(PublicCode.MULTI_102974.getErrCode(), PublicCode.MULTI_102974.getMessage());
        // SessionUserInfo sessionUserInfo = sessionHelper.getSessionUserInfo(request);
        // ExpireGoodsDTO expireGoodsDTO = new ExpireGoodsDTO(entityId, null, sessionUserInfo.getCustomerId());
        // expireGoodsDTO.setTimeStamp(timeStamp);
        // expireGoodsDTO.setBusinessScenario(PresellCartTypeEnum.PRESELL.getType());
        // Result<List<ExpiredGoodsVO>> expiredGoodsList = presellClientCartFacade.getExpiredGoods(expireGoodsDTO);
        // if (!ResultUtil.isResultSuccess(expiredGoodsList)) {
        //     return ApiResultUtil.failResult(expiredGoodsList.getResultCode(), expiredGoodsList.getMessage());
        // }
        // return ApiResultUtil.successResult(expiredGoodsList.getModel());
    }

    /**
     * 获取用户 id
     *
     * @param request request
     * @return 用户 id
     */
    private String getCustomerId(HttpServletRequest request) {
        SessionUserInfo sessionUserInfo = sessionHelper.getSessionUserInfo(request);
        if (null == sessionUserInfo) {
            throw new NullPointerException("用户id为null");
        }
        return sessionUserInfo.getCustomerId();
    }
}
