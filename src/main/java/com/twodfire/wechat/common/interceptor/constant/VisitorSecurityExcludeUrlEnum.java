/*
 * Copyright (C) 2009-2017 Hangzhou 2Dfire Technology Co., Ltd.All rights reserved
 */
package com.twodfire.wechat.common.interceptor.constant;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * VisitorSecurityUrlEnum
 *
 * <AUTHOR>
 * @date 2018/6/4
 * desc：StringUtils.containsOnly 严格匹配模式，一个符号都不可以错
 */
public enum VisitorSecurityExcludeUrlEnum {

    ADS("presell/v1/get_presell_list"),
    GSB("/mini-app/qr_code/v1/wechat/get_scan_bean"),
    IVT("/mini-app/users/v1/is_valid_token"),
//    BPI("/mini-app/users/v1/visitor/brand_publicity_info"),
    RMS("/mini-app/shop/v1/recommend_menus"),
    UUI("/mini-app/users/v1/ma/up_user_info"),
    SIVG("/shop/info/v1/get_shop_delicacy"),
    MOVD("/mini-app/oauth/v1/del"),
    MOVDT("/mini-app/oauth/v1/del_token"),
    SIVP("/shop/info/v1/p0"),
    UUIA("/mini-app/users/v1/ma/up_user_info/alipay"),
    SQF("/shop/v1/query_conf"),
    MHTBG("/mini-app/high/train/bj/get_scan_bean"),
    PVPI("/pay/v1/prepaid_info"),
    LIVING("/mini-app/living/list_room"),
    OGO("/oauth/get_openId"),
    SEAT_PREPAY("/shop/v1/get_seat_prepay_status"),
    WEWORK_CONTACT_INFO("/wework/contact_info");


    VisitorSecurityExcludeUrlEnum(String urlPath) {
        this.urlPath = urlPath;
    }

    public static boolean include(String urlPath) {
        if (StringUtils.startsWithIgnoreCase(urlPath, "/weixin-meal")) {
            urlPath = StringUtils.replaceIgnoreCase(urlPath, "/weixin-meal/", StringUtils.EMPTY);
        }
        String finalUrl = urlPath;
        return Arrays.stream(VisitorSecurityExcludeUrlEnum.values())
                .filter(visitorSecurityExcludeUrlEnum -> StringUtils.containsOnly(visitorSecurityExcludeUrlEnum.getUrlPath(), finalUrl))
                .findAny().isPresent();
    }

    private String urlPath;


    public String getUrlPath() {
        return urlPath;
    }

    public void setUrlPath(String urlPath) {
        this.urlPath = urlPath;
    }

}

    