/*
 * Copyright (C) 2009-2016 Hangzhou 2Dfire Technology Co., Ltd. All rights reserved
 */
package com.twodfire.wechat.facade;

import com.dfire.consumer.util.LoggerUtil;
import com.dfire.soa.control.util.BlackListsServiceUtil;
import com.twodfire.wechat.common.logger.LoggerMarkers;
import com.twodfire.wechat.common.logger.WXLoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * ControlClientFacade
 *
 * <AUTHOR>
 * @since 2017-08-05
 */
@Service
public class ControlClientFacade {
    @Resource
    private BlackListsServiceUtil blackListsServiceUtil;

    public boolean isBlackUser(String customerRegisterId, String entityId) {
        boolean isBlackUser = blackListsServiceUtil.isBlackUser(customerRegisterId, entityId);
        if (isBlackUser) {
            LoggerUtil.warn(WXLoggerFactory.BLACk_LIST, LoggerMarkers.BLACK_LIST_ENTITY_ERROR, "店铺黑名单用户,customerId:{},entityId:{}", customerRegisterId, entityId);
        }
        return isBlackUser;
    }
}
