/*
 * Copyright (C) 2009-2017 Hangzhou 2Dfire Technology Co., Ltd.All rights reserved
 */
package com.twodfire.wechat.facade;

import com.dfire.consumer.util.exception.DubboBizException;
import com.twodfire.share.result.Result;
import com.twodfire.share.util.ResultUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * ConsumerActivityFacade
 *
 * <AUTHOR>
 * @date 2018/3/15
 * desc：
 */
@Service
public class ConsumerActivityFacade {
    // @Resource
    // private IEntityPresellPreviewFacade entityPresellPreviewFacade;
    // @Resource
    // private IPresellAttentionShopFacade presellAttentionShopFacade;
    // @Resource
    // private IPresellSeatConfigFacade presellSeatConfigFacade;


    /**
     * 关注店铺
     *
     * @param customerId
     * @param entityId
     * @return
     */
    public boolean attentionShop(String customerId, String entityId) {
        // Result<PresellAttentionShopVO> result = presellAttentionShopFacade.attentionShop(customerId, entityId);
        // if (ResultUtil.isResultSuccess(result)) {
        //     return null != result.getModel();
        // }
        // throw new DubboBizException(result.getResultCode(), result.getMessage());

        return false;
    }


    /**
     * 取消关注
     *
     * @param customerId
     * @param entityId
     * @return
     */
    public boolean cancelAttentionShop(String customerId, String entityId) {
        // Result<Boolean> result = presellAttentionShopFacade.cancelAttentionShop(customerId, entityId);
        // if (ResultUtil.isResultSuccess(result)) {
        //     return result.getModel();
        // }
        // throw new DubboBizException(result.getResultCode(), result.getMessage());
        return false;
    }

}

    