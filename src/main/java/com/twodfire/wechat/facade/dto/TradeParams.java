/*
 * Copyright (C) 2009-2016 Hangzhou 2Dfire Technology Co., Ltd. All rights reserved
 */
package com.twodfire.wechat.facade.dto;

import com.dfire.soa.oauth.common.constants.ClientSourceEnum;
import com.dfire.soa.oauth.common.constants.ThirdPartyUAType;
import com.dfire.soa.oauth.common.domain.SessionUserInfo;
import com.dfire.tp.client.constant.FromType;
import com.dfire.tp.client.constant.RequestFrom;
import com.dfire.tp.client.payment.request.param.TradeParam;
import com.twodfire.wechat.common.local.RequestManager;
import com.twodfire.wechat.constant.Constants;
import com.twodfire.wechat.utils.AuthUaUtil;
import com.twodfire.wechat.utils.IpUtil;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;

/**
 * TradeParams
 *
 * <AUTHOR>
 * @since 2017-08-07
 */
public class TradeParams {

    public static TradeParam fillTradeParam(SessionUserInfo sessionUserInfo, TradeParam tradeParam) {
        HttpServletRequest request = RequestManager.getHttpServletRequest();
        if (null != sessionUserInfo) {
            tradeParam.setCustomerRegisterId(sessionUserInfo.getCustomerId());
            //判断是否是小程序
            boolean wxAppletFlag = StringUtils.equalsIgnoreCase(sessionUserInfo.getClientSource(), ClientSourceEnum.MINI_PROGRAM_CLIENT.getCode())
                    ||StringUtils.equalsIgnoreCase(sessionUserInfo.getClientSource(), ClientSourceEnum.HPP_MINI_PROGRAM_CLIENT.getCode());
            boolean aliPayAppletFlag = StringUtils.equalsIgnoreCase(sessionUserInfo.getClientSource(), ClientSourceEnum.ALIPAY_MINI_PROGRAM_CLIENT.getCode());
            boolean douyinAppletFlag = StringUtils.equalsIgnoreCase(sessionUserInfo.getClientSource(), ClientSourceEnum.BYTEDANCE_MINI_PROGRAM_CLIENT.getCode());
            if (wxAppletFlag) {
                tradeParam.setFromType(FromType.WEIXIN_APPLET.getCode());
            }
            if (aliPayAppletFlag) {
                tradeParam.setFromType(FromType.ALIPAY_H5.getCode());
            }
            if (douyinAppletFlag) {
                tradeParam.setFromType(FromType.DOUYIN_APPLET.getCode());
            }

            if (wxAppletFlag || aliPayAppletFlag) {
                tradeParam.setOpenId(StringUtils.isNotBlank(sessionUserInfo.getMiniProgramMealOpenid()) ? sessionUserInfo.getMiniProgramMealOpenid() : StringUtils.EMPTY);
                tradeParam.setLargeOpenid(StringUtils.isNotBlank(sessionUserInfo.getMiniProgramMealOpenid()) ? sessionUserInfo.getMiniProgramMealOpenid() : StringUtils.EMPTY);
            }
            if (StringUtils.isBlank(tradeParam.getOpenId())) {
                tradeParam.setOpenId(StringUtils.isNotBlank(sessionUserInfo.getOpenid()) ? sessionUserInfo.getOpenid() : StringUtils.EMPTY);
            }
            if (StringUtils.isBlank(tradeParam.getLargeOpenid())) {
                tradeParam.setLargeOpenid(StringUtils.isNotBlank(sessionUserInfo.getLargeOpenid()) ? sessionUserInfo.getLargeOpenid() : StringUtils.EMPTY);
            }
            tradeParam.setSubAppId(StringUtils.isBlank(sessionUserInfo.getAppId()) ? tradeParam.getSubAppId() : sessionUserInfo.getAppId());
        }
        tradeParam.setRemoteAddr(IpUtil.getRemoteIp(request));
        if (tradeParam.getRequestUA() != null && "iot".equals(tradeParam.getRequestUA())) {
            //蜻蜓自助点餐
            tradeParam.setRequestUA("8");
        } else {
            tradeParam.setRequestUA(request.getHeader(Constants.Http.UA));
        }
        tradeParam.setRequestFrom(RequestFrom.H5.getFrom());
        //前端如果有传值，就用前端的，否则后端兼容
        if (tradeParam.getFromType() != null) {
            return tradeParam;
        }
        ThirdPartyUAType thirdPartyUAType = AuthUaUtil.getThirdPartyUAType(request.getHeader(Constants.Http.UA));
        if (null != thirdPartyUAType) {
            if (StringUtils.equalsIgnoreCase(ThirdPartyUAType.WEIXIN_UA.getCode(), thirdPartyUAType.getCode())) {
                tradeParam.setFromType(FromType.WEIXIN_H5.getCode());
            } else if (StringUtils.equalsIgnoreCase(ThirdPartyUAType.ALIPAY_UA.getCode(), thirdPartyUAType.getCode())) {
                tradeParam.setFromType(FromType.ALIPAY_H5.getCode());
            } else if (StringUtils.equalsIgnoreCase(ThirdPartyUAType.QQ_UA.getCode(), thirdPartyUAType.getCode())) {
                tradeParam.setFromType(FromType.QQ_H5.getCode());
            } else if (StringUtils.equalsIgnoreCase(ThirdPartyUAType.CLOUD_PAY_UA.getCode(), thirdPartyUAType.getCode())) {
                tradeParam.setFromType(FromType.UNION_PAY_INLINE_H5.getCode());
            }
        }
        return tradeParam;
    }
}
