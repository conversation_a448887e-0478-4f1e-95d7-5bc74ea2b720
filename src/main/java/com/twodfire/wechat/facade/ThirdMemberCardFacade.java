package com.twodfire.wechat.facade;

import com.alibaba.common.convert.Convert;
import com.dfire.consumer.util.LoggerUtil;
import com.dfire.consumer.util.exception.DubboBizException;
import com.dfire.consumer.util.exception.DubboRPCException;
import com.dfire.soa.thirdcrm.bo.BindResultBO;
import com.dfire.soa.thirdcrm.bo.ThirdBindBO;
import com.dfire.soa.thirdcrm.bo.ThirdCrmSystemBO;
import com.dfire.soa.thirdcrm.bo.degree.DegreeBO;
import com.dfire.soa.thirdcrm.request.QueryBindRequest;
import com.dfire.soa.thirdcrm.request.ThirdBindDeleteRequest;
import com.dfire.soa.thirdcrm.request.ThirdBindRequest;
import com.dfire.soa.thirdcrm.service.IThirdBindService;
import com.dfire.soa.thirdcrm.service.IThirdCrmApplyShopService;
import com.twodfire.share.result.Result;
import com.twodfire.share.util.ResultUtil;
import com.twodfire.wechat.common.logger.LoggerMarkers;
import com.twodfire.wechat.common.logger.WXLoggerFactory;
import com.twodfire.wechat.vo.card.ThirdCardStatusVo;
import com.twodfire.wechat.vo.form.ThirdCardBindForm;
import com.twodfire.wechat.vo.member.ThirdBindVO;
import com.twodfire.wechat.vo.member.ThirdCrmSystemVO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static com.dfire.consumer.i18n.code.PublicCode.MULTI_101550;

/**
 * <AUTHOR>
 * @Description
 * @Date 2018/10/22 10:43
 **/
@Service
public class ThirdMemberCardFacade {

    @Resource
    private IThirdBindService iThirdBindService;

    @Resource
    private IThirdCrmApplyShopService iThirdCrmApplyShopService;


    /**
     * 查询店铺开通的第三方机构的列表
     *
     * @param entityId 店铺Id
     * @return 第三方机构列表
     */
    public List<ThirdCrmSystemVO> queryThirdCrmSystemList(String entityId) {
        Result<List<ThirdCrmSystemBO>> result;
        try {
            result = iThirdCrmApplyShopService.queryIsSelfUseThirdCrmSystemList(entityId);
        } catch (Exception e) {
            throw new DubboRPCException(e);
        }
        if (result == null) {
            LoggerUtil.warn(WXLoggerFactory.CARD_LOGGER, LoggerMarkers.THIRD_CARD, "ThirdMemberCardFacade.queryIsSelfUseThirdCrmSystemList，" +
                    "entityId=" + entityId);
            throw new DubboBizException(MULTI_101550.getErrCode(), MULTI_101550.getMessage());
        }
        if (!ResultUtil.isModelNotNull(result)) {
            LoggerUtil.warn(WXLoggerFactory.CARD_LOGGER, LoggerMarkers.THIRD_CARD, "ThirdMemberCardFacade.queryIsSelfUseThirdCrmSystemList，" +
                    "entityId=" + entityId + ",errCode:" + result.getResultCode() + ",errMsg:" + result.getMessage());
            throw new DubboBizException(result.getResultCode(), result.getMessage());
        }
        List<ThirdCrmSystemVO> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(result.getModel())) {
            for (ThirdCrmSystemBO thirdCrmSystemBO : result.getModel()) {
                ThirdCrmSystemVO thirdCrmSystemVO = new ThirdCrmSystemVO();
                thirdCrmSystemVO.setOrgName(thirdCrmSystemBO.getEntityName());
                thirdCrmSystemVO.setOrgId(thirdCrmSystemBO.getThirdCrmSystemId());
                list.add(thirdCrmSystemVO);
            }
        }
        return list;
    }


    /**
     * 查询用户绑定的外部会员卡列表
     *
     * @param entityId   店铺Id
     * @param customerId 会员Id
     * @return 第三方会员卡列表
     */
    public List<ThirdBindVO> queryBindList(String entityId, String customerId) {
        QueryBindRequest queryBindRequest = new QueryBindRequest();
        queryBindRequest.setTwodfireMemberId(customerId);
        queryBindRequest.setShopEntityId(entityId);
        Result<List<ThirdBindBO>> result = null;
        try {
            result = iThirdBindService.queryBindList(queryBindRequest);
        } catch (Exception e) {
            throw new DubboRPCException(e);
        }
        if (result == null) {
            LoggerUtil.warn(WXLoggerFactory.CARD_LOGGER, LoggerMarkers.THIRD_CARD, "ThirdMemberCardFacade.queryBindList，" +
                    "entityId=" + entityId + ",customerId:" + customerId);
            throw new DubboBizException(MULTI_101550.getErrCode(), MULTI_101550.getMessage());
        }
        if (!ResultUtil.isModelNotNull(result)) {
            LoggerUtil.warn(WXLoggerFactory.CARD_LOGGER, LoggerMarkers.THIRD_CARD, "ThirdMemberCardFacade.queryBindList，" +
                    "entityId=" + entityId + ",customerId:" + customerId + ",errCode:" + result.getResultCode() + ",errMsg:" + result.getMessage());
            throw new DubboBizException(result.getResultCode(), result.getMessage());
        }
        List<ThirdBindVO> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(result.getModel())) {
            for (ThirdBindBO thirdBindBO : result.getModel()) {
                ThirdBindVO thirdBindVO = new ThirdBindVO();
                thirdBindVO.setCardCode(thirdBindBO.getCardCode());
                thirdBindVO.setOrgId(Convert.asString(thirdBindBO.getThirdCrmSystemId()));
                thirdBindVO.setOrgName(thirdBindBO.getEntityName());
                thirdBindVO.setId(Convert.asString(thirdBindBO.getId()));
                thirdBindVO.setKindCardName(thirdBindBO.getKindCardName());
                thirdBindVO.setAccountCode(thirdBindBO.getAccountCode());
                list.add(thirdBindVO);
            }
        }
        return list;
    }

    /**
     * 绑定第三方会员卡
     *
     * @param thirdCardBindForm 页面绑定表单
     * @return 成功---true   失败---false
     */
    public boolean bindThirdCard(ThirdCardBindForm thirdCardBindForm) {
        ThirdBindRequest thirdBindRequest = new ThirdBindRequest();
        thirdBindRequest.setThirdCrmSystemId(thirdCardBindForm.getOrgId());
        thirdBindRequest.setThirdMobile(thirdCardBindForm.getMobile());
        thirdBindRequest.setCardCode(thirdCardBindForm.getCardCode());
        thirdBindRequest.setTwodfireMemberId(thirdCardBindForm.getCustomerId());
        Result result;
        try {
            result = iThirdBindService.bind(thirdBindRequest);
        } catch (Exception e) {
            throw new DubboRPCException(e);
        }
        if (result == null) {
            LoggerUtil.warn(WXLoggerFactory.CARD_LOGGER, LoggerMarkers.THIRD_CARD, "ThirdMemberCardFacade.bind，"
                    + thirdCardBindForm.toString());
            throw new DubboBizException(MULTI_101550.getErrCode(), MULTI_101550.getMessage());
        }
        if (!ResultUtil.isResultSuccess(result)) {
            LoggerUtil.warn(WXLoggerFactory.CARD_LOGGER, LoggerMarkers.THIRD_CARD, "ThirdMemberCardFacade.bind，"
                    + thirdCardBindForm.toString() + ",errCode:" + result.getResultCode() + ",errMsg:" + result.getMessage());
            throw new DubboBizException(result.getResultCode(), result.getMessage());
        }
        return true;
    }

    /**
     * 绑定第三方用户
     *
     * @return 成功---true   失败---false
     */
    public boolean bindThirdMember(String twodfireMemberId, String thirdMemberId, String entityId) {
        ThirdBindRequest thirdBindRequest = new ThirdBindRequest();
        if (entityId.startsWith("99")) {
            thirdBindRequest.setThirdCrmSystemId(21L);
        } else {
            thirdBindRequest.setThirdCrmSystemId(9L);
        }
        thirdBindRequest.setThirdMemberId(thirdMemberId);
        thirdBindRequest.setTwodfireMemberId(twodfireMemberId);
        Result result;
        try {
            result = iThirdBindService.bind(thirdBindRequest);
        } catch (Exception e) {
            throw new DubboRPCException(e);
        }
        if (result == null) {
            LoggerUtil.warn(WXLoggerFactory.CARD_LOGGER, LoggerMarkers.THIRD_CARD, "bindThirdMember.bind，"
                    + thirdBindRequest.toString());
            throw new DubboBizException(MULTI_101550.getErrCode(), MULTI_101550.getMessage());
        }
        if (!ResultUtil.isResultSuccess(result)) {
            LoggerUtil.warn(WXLoggerFactory.CARD_LOGGER, LoggerMarkers.THIRD_CARD, "bindThirdMember.bind，"
                    + thirdBindRequest.toString() + ",errCode:" + result.getResultCode() + ",errMsg:" + result.getMessage());
            throw new DubboBizException(result.getResultCode(), result.getMessage());
        }
        return true;
    }

    /**
     * 获取第三方积分
     */
    public Integer bindThirdTotalDegree(String twodfireMemberId, String entityId) {
        QueryBindRequest queryBindRequest = new QueryBindRequest();
        if (entityId.startsWith("99")) {
            queryBindRequest.setThirdCrmSystemId(21L);
        } else {
            queryBindRequest.setThirdCrmSystemId(9L);
        }
        queryBindRequest.setTwodfireMemberId(twodfireMemberId);
//        queryBindRequest.setShopEntityId(entityId);
        Result<List<DegreeBO>> result;
        try {
            result = iThirdBindService.queryBoundThirdDegree(queryBindRequest);
        } catch (Exception e) {
            return 0;
        }
        if (ResultUtil.isResultSuccess(result) && CollectionUtils.isNotEmpty(result.getModel())) {
            return result.getModel().get(0).getTotalDegree();
        }
        return 0;
    }


    /**
     * 删除用户绑定的第三方会员卡
     *
     * @param id         第三方会员卡Id(查绑定的第三方会员卡返回信息中的那个id)
     * @param customerId 会员Id
     * @return 成功----true   失败----false
     */
    public boolean deleteThirdCard(Long id, String customerId) {
        ThirdBindDeleteRequest thirdBindDeleteRequest = new ThirdBindDeleteRequest();
        thirdBindDeleteRequest.setId(id);
        thirdBindDeleteRequest.setTwodfireMemberId(customerId);
        Result result = null;
        try {
            result = iThirdBindService.delete(thirdBindDeleteRequest);
        } catch (Exception e) {
            throw new DubboRPCException(e);
        }
        if (result == null) {
            LoggerUtil.warn(WXLoggerFactory.CARD_LOGGER, LoggerMarkers.THIRD_CARD, "ThirdMemberCardFacade.delete，" +
                    "id=" + id + ",customerId:" + customerId);
            throw new DubboBizException(MULTI_101550.getErrCode(), MULTI_101550.getMessage());
        }
        if (!ResultUtil.isResultSuccess(result)) {
            LoggerUtil.warn(WXLoggerFactory.CARD_LOGGER, LoggerMarkers.THIRD_CARD, "ThirdMemberCardFacade.delete，" +
                    "id=" + id + ",customerId:" + customerId + ",errCode:" + result.getResultCode() + ",errMsg:" + result.getMessage());
            throw new DubboBizException(result.getResultCode(), result.getMessage());
        }
        return true;
    }

    /**
     * @param entityId   店铺Id
     * @param customerId 会员Id
     * @return 外部卡的业态（商圈、校园）、外部卡开通/绑定状态(0-未开通, 1:已绑定, 2:未绑定)
     */
    public ThirdCardStatusVo getThirdCardOpenStatus(String entityId, String customerId) {
        QueryBindRequest queryBindRequest = new QueryBindRequest();
        queryBindRequest.setTwodfireMemberId(customerId);
        queryBindRequest.setShopEntityId(entityId);
        Result<BindResultBO> result = iThirdBindService.queryBindResult(queryBindRequest);
        if (result == null) {
            LoggerUtil.warn(WXLoggerFactory.CARD_LOGGER, LoggerMarkers.THIRD_CARD, "ThirdMemberCardFacade.getThirdCardOpenStatus，" +
                    "entityId=" + entityId + ",customerId:" + customerId);
            throw new DubboBizException(MULTI_101550.getErrCode(), MULTI_101550.getMessage());
        }
        if (!ResultUtil.isResultSuccess(result)) {
            LoggerUtil.warn(WXLoggerFactory.CARD_LOGGER, LoggerMarkers.THIRD_CARD, "ThirdMemberCardFacade.getThirdCardOpenStatus，" +
                    "entityId=" + entityId + ",customerId:" + customerId + ",errCode:" + result.getResultCode() + ",errMsg:" + result.getMessage());
            throw new DubboBizException(result.getResultCode(), result.getMessage());
        }
        BindResultBO model = result.getModel();
        ThirdCardStatusVo thirdCardStatusVo = new ThirdCardStatusVo();
        if (model != null) {
            thirdCardStatusVo.setThirdCardOpenStatus(model.getBindStatus() == null ? 0 : model.getBindStatus().intValue());
            thirdCardStatusVo.setThirdCardType(model.getIndustryType() == null ? 0 : model.getIndustryType().intValue());
        }
        return thirdCardStatusVo;
    }
}
