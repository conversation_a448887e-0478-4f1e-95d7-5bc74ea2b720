/*
 * Copyright (C) 2009-2016 Hangzhou 2Dfire Technology Co., Ltd. All rights reserved
 */
package com.twodfire.wechat.vo.activity;

import java.io.Serializable;

/**
 * HotActivityShareInfoVo
 *
 * <AUTHOR>
 * @since 2017-11-30
 */
public class HotActivityShareInfoVo implements Serializable {

    private static final long serialVersionUID = 5220193259003298525L;
    /**
     * 分享给朋友圈的信息
     */
    HotActivityShareInfoVo.ActivityShareVo moment;
    /**
     * 分享给朋友的信息
     */
    HotActivityShareInfoVo.ActivityShareVo friend;

    public HotActivityShareInfoVo(ActivityShareVo moment, ActivityShareVo friend) {
        this.moment = moment;
        this.friend = friend;
    }

    public ActivityShareVo getMoment() {
        return moment;
    }

    public void setMoment(ActivityShareVo moment) {
        this.moment = moment;
    }

    public ActivityShareVo getFriend() {
        return friend;
    }

    public void setFriend(ActivityShareVo friend) {
        this.friend = friend;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }


    public static class ActivityShareVo implements Serializable, Cloneable {
        /**
         * 分享title
         */
        private String title;
        /**
         * 分享备注内容
         */
        private String memo;
        /**
         * 分享LogURL
         */
        private String imgUrl;
        /**
         *分享授权URL
         */
        private String shareUrl;

        @Override
        public Object clone() {
            HotActivityShareInfoVo.ActivityShareVo activityShareVo = null;
            try {
                activityShareVo = (HotActivityShareInfoVo.ActivityShareVo) super.clone();
            } catch (CloneNotSupportedException e) {
                e.printStackTrace();
            }
            return activityShareVo;
        }


        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getMemo() {
            return memo;
        }

        public void setMemo(String memo) {
            this.memo = memo;
        }

        public String getImgUrl() {
            return imgUrl;
        }

        public void setImgUrl(String imgUrl) {
            this.imgUrl = imgUrl;
        }

        public String getShareUrl() {
            return shareUrl;
        }

        public void setShareUrl(String shareUrl) {
            this.shareUrl = shareUrl;
        }
    }
}