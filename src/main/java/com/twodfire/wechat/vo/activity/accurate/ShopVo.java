package com.twodfire.wechat.vo.activity.accurate;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

/**
 * Created by baijiang  on 17-11-2下午5:25.
 * mail:<a href="<EMAIL>"/>
 * Desc:
 */
public class ShopVo implements Serializable {

    @JSO<PERSON>ield(name = "shop_name")
    private String shopName;
    @JSONField(name = "shop_address")
    private String shopAddress;
    @JSONField(name = "shop_address")
    private String shopLogo;

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getShopAddress() {
        return shopAddress;
    }

    public void setShopAddress(String shopAddress) {
        this.shopAddress = shopAddress;
    }

    public String getShopLogo() {
        return shopLogo;
    }

    public void setShopLogo(String shopLogo) {
        this.shopLogo = shopLogo;
    }
}
