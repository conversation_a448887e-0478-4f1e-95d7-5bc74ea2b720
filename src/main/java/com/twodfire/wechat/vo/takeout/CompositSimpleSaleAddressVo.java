package com.twodfire.wechat.vo.takeout;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * Created by bingli on 2016/8/31 0031.
 */
public class CompositSimpleSaleAddressVo {
    @JsonProperty("save_sale_address_id")
    @JSONField(name = "save_sale_address_id")
    private String id;
    @JsonProperty("sale_address_list")
    @JSONField(name = "sale_address_list")
    private List<SimpleSaleAddressVo> simpleSaleAddressVoList;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public List<SimpleSaleAddressVo> getSimpleSaleAddressVoList() {
        return simpleSaleAddressVoList;
    }

    public void setSimpleSaleAddressVoList(List<SimpleSaleAddressVo> simpleSaleAddressVoList) {
        this.simpleSaleAddressVoList = simpleSaleAddressVoList;
    }
}
