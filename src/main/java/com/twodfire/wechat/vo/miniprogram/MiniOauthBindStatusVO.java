/*
 * Copyright (C) 2009-2016 Hangzhou 2Dfire Technology Co., Ltd. All rights reserved
 */
package com.twodfire.wechat.vo.miniprogram;

import java.io.Serializable;

/**
 * MiniOauthBindStatusVO
 *
 * <AUTHOR>
 * @since 2018-07-24
 */
public class MiniOauthBindStatusVO implements Serializable{

    private static final long serialVersionUID = 1991207130923286189L;

    /**
     * //换绑状态； 1：成功  2：wechat-soa换绑失败  3： member-soa换绑失败 4：其他失败
     */
    private Integer bindStatus;

    public Integer getBindStatus() {
        return bindStatus;
    }

    public void setBindStatus(Integer bindStatus) {
        this.bindStatus = bindStatus;
    }

}