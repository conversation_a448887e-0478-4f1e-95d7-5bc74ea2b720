/*
 * Copyright (C) 2009-2016 Hangzhou 2Dfire Technology Co., Ltd. All rights reserved
 */
package com.twodfire.wechat.vo.miniprogram;

import java.io.Serializable;

/**
 * MiniOauthJumpStatusVO
 *
 * <AUTHOR>
 * @since 2018-07-24
 */
public class MiniOauthCancelStatusVO implements Serializable {

    private static final long serialVersionUID = 443761371006092277L;
    /**
     * 取消状态：； 1：成功  2：失败
     */
    private Integer cancelStatus;

    public Integer getCancelStatus() {
        return cancelStatus;
    }

    public void setCancelStatus(Integer cancelStatus) {
        this.cancelStatus = cancelStatus;
    }
}