<?xml version="1.0" encoding="UTF-8"?>
<!-- Bean头部 -->
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-4.0.xsd">

    <!-- session cache -->
    <bean id="sessionService" class="com.twodfire.wechat.common.cache.SentinelCacheService" init-method="init"
          destroy-method="destroy">
        <property name="sentinels" value="${session.redis.server}"/>
        <property name="masterName" value="${session.redis.masterName}"/>
        <property name="database" value="${session.redis.database}"/>
        <property name="maxWaitMillis" value="${session.redis.maxWaitMillis}"/>
    </bean>

    <!-- 直连配置 -->
    <bean id="wxMpConfigStorage" class="com.dfire.wt.mp.api.WxMpInRedisConfigStorage">
        <property name="appId" value="${wechat.direct.appId}"/>
        <property name="secret" value="${wechat.direct.secret}"/>
        <property name="aesKey" value="${wechat.aesKey}"/>
        <property name="token" value="${wechat.token}"/>
        <property name="redisService" ref="sessionService"/>
    </bean>

    <bean id="wxMpHelper" class="com.twodfire.wechat.helper.WxMpHelper">
        <property name="maxRetryTimes" value="0"/>
        <property name="wxMpConfigStorage" ref="wxMpConfigStorage"/>
    </bean>

    <bean id="wxFanMpConfigStorage" class="com.dfire.wt.mp.api.WxMpInRedisConfigStorage">
        <property name="appId" value="${wechat.fan.appId}"/>
        <property name="secret" value="${wechat.fan.appSecret}"/>
        <property name="redisService" ref="sessionService"/>
    </bean>

    <bean id="weChatFanHelper" class="com.twodfire.wechat.helper.WxMpHelper">
        <property name="maxRetryTimes" value="0"/>
        <property name="wxMpConfigStorage" ref="wxFanMpConfigStorage"/>
    </bean>

    <bean id="activityProperties" class="com.twodfire.wechat.props.ActivityProperties">
        <property name="supportRechargeEntityId" value="${support.recharge.entityid}"/>
    </bean>
    <bean id="queueProperties" class="com.twodfire.wechat.props.QueueProperties">
        <property name="queueCallbackUrl" value="${wechat.queue.url}"/>
    </bean>
    <!--分享、转赠相关-->
    <bean id="privilegeShareProperties" class="com.twodfire.wechat.props.PrivilegeShareProperties">
        <property name="privilegeShareCallbackUrl" value="${wechat.privilege.share.url}"/>
    </bean>
    <bean id="privilegeTransferProperties" class="com.twodfire.wechat.props.PrivilegeTransferProperties">
        <property name="privilegeTransferCallbackUrl" value="${wechat.privilege.transfer.url}"/>
    </bean>

    <bean id="lotteryActivityShareProperties" class="com.twodfire.wechat.props.LotteryActivityShareProperties">
        <property name="lotteryActivityShareCallbackUrl" value="${wechat.lottery.share.url}"/>
        <property name="lotteryActivityOauthUrl" value="${wechat.lottery.oauth.url}"/>
    </bean>
    <bean id="hotActivityShareProperties" class="com.twodfire.wechat.props.HotActivityShareProperties">
        <property name="hotActivityShareCallbackUrl" value="${wechat.hot.share.h5.url}"/>
        <property name="hotActivityOauthUrl" value="${wechat.hot.oauth.url}"/>
    </bean>

    <bean id="storeProperties" class="com.twodfire.wechat.props.StoreOauthProperties">
        <property name="storreCallbackUrl" value="${wechat.store.url}"/>
        <property name="bossGotoShopHomeUrl" value="${boss.goto.shop.home.url}"/>
    </bean>
    <!--有好菜文章分享-->
    <bean id="albumDetailProperties" class="com.twodfire.wechat.props.AlbumDetailProperties">
        <property name="albumDetailUrl" value="${wechat.album.url}"/>
    </bean>
    <!-- 分享URL-->
    <bean id="activitiesProperties" class="com.twodfire.wechat.props.ActivitiesProperties">
        <property name="newMbrGiftShareCallbackUrl" value="${activities.newmbrgift.share.url}"/>
        <property name="openCardShareCallbackUrl" value="${activities.opencard.share.url}"/>
        <property name="miniGameShareCallbackUrl" value="${activities.minigame.share.url}"/>
        <property name="consumerGiftShareCallbackUrl" value="${activities.consumergift.share.url}"/>
        <property name="memberSystemShopPushUrl" value="${activities.membersystem.shoppush.share.url}"/>
        <property name="companyCardCodeGotoH5Url" value="${company.card.code.goto.h5.url}"/>
        <property name="salesStackBaseUrl" value="${sales.stack.base.url}"/>
        <property name="applyFireCard2H5Url" value="${apply.firecard.goto.h5.url}"/>
        <property name="applyFireCardUrl" value="${apply.firecard.url}"/>
        <property name="hotpotFestivalUrl" value="${hot.pot.festival.url}"/>
        <property name="flipCardShareCallBackUrl" value="${activities.flip.card.share.url}"/>
        <property name="nosActivityListCallBackUrl" value="${activities.nos.list.share.url}"/>
        <property name="nosActivityDetailCallBackUrl" value="${activities.nos.detail.share.url}"/>
        <property name="nosActivityShareEntityId" value="${activities.nos.share.entityId}"/>
        <property name="carveUpCouponCallBackUrl" value="${activities.carve.up.coupon.share.url}"/>
        <property name="queueGiftShareCallbackUrl" value="${activities.queue.gift.share.url}"/>
        <property name="microAgentItemErrorUrl" value="${agent.item.error.url}"/>
    </bean>
    <bean id="fireMemberUrlProperties" class="com.twodfire.wechat.props.FireMemberUrlProperties">
        <property name="vipEntityId" value="${firemember.vip.entityId}"/>
        <property name="fireMemberCompareUrl" value="${wechat.fire.member.compare.url}"/>
        <property name="fireMemberFirstPageCallBackUrl" value="${wechat.firemember.firstpage.url}"/>
        <property name="fireMemberAchievementInfoUrl" value="${wechat.firemember.achievementshareinfo.url}"/>
        <property name="fireMemberRechargeVipUrl" value="${wechat.firemember.viprecharge.url}"/>
    </bean>
    <!-- 菜品详情页 分享URL -->
    <bean id="itemProperties" class="com.twodfire.wechat.props.ItemProperties">
        <property name="detailShareCallbackUrl" value="${item.detail.share.url}"/>
    </bean>

    <!-- 系统参数配置 -->
    <bean id="applicationProperties" class="com.twodfire.wechat.props.ApplicationProperties">
        <property name="mealServerUri" value="${meal.server.uri}"/>
        <property name="qrcodeLongurl" value="${qrcode.longurl}"/>
    </bean>

    <!-- 短信模板配置 -->
    <bean id="messageProperties" class="com.twodfire.wechat.props.MessageProperties">
        <property name="reserveSuccessCode" value="${reserve.success.code}"/>
    </bean>

    <!-- 预订配置 -->
    <bean id="reservationProperties" class="com.twodfire.wechat.props.ReservationProperties">
        <property name="jumpMp" value="${jump.mp}"/>
    </bean>

    <!-- 零售店铺配置 -->
    <bean id="retailShopProperties" class="com.twodfire.wechat.props.RetailShopProperties">
        <property name="originalShareCallbackUrl" value="${retail.shop.original.share.url}"/>
        <property name="shareCallbackUrl" value="${retail.shop.share.url}"/>
    </bean>

    <!-- 小程序业务配置 -->
    <bean id="businessConfigProperties" class="com.twodfire.wechat.props.miniprogram.BusinessConfigProperties">
        <property name="mealShortDomain" value="${business.meal.short.domain}"/>
        <property name="mealPageSplitUrl" value="${business.meal.page.split.url}"/>
        <property name="mealPageSplitPrefix" value="${business.meal.page.split.prefix}"/>
        <property name="maDowngradeUrl" value="${business.ma.downgrade.url}"/>
        <property name="mealPlatformMaAppid" value="${business.meal.platform.ma.appid}"/>
    </bean>


</beans>