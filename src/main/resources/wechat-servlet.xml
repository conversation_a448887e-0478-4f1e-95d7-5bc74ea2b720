<?xml version="1.0" encoding="UTF-8"?>
<!-- <PERSON>头部 -->
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p" xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
        http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
		http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc-4.0.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">

    <aop:aspectj-autoproxy proxy-target-class="true"/>
    <!-- 配置注解扫描 -->
    <context:annotation-config/>

    <!-- 静态资源-->
    <mvc:default-servlet-handler/>

    <!-- 自动扫描的包名 -->
    <context:component-scan base-package="com.twodfire.wechat.api" use-default-filters="false">
        <context:include-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
    </context:component-scan>

    <!-- swagger -->
<!--    <import resource="application-swagger.xml"/>-->
    <mvc:interceptors>

        <!-- H5用户授权的拦截 -->
        <mvc:interceptor>
            <mvc:mapping path="/thirdCards/**"/>
            <mvc:mapping path="/activity/v1/**"/>
            <mvc:mapping path="/hot_activity/**"/>
            <mvc:mapping path="/lottery/**"/>
            <mvc:mapping path="/adv/**"/>
            <mvc:mapping path="/enterprise/business/**"/>
            <mvc:mapping path="/fm/**"/>
            <mvc:mapping path="/hotpot/**"/>
            <mvc:mapping path="/menus/**"/>
            <mvc:mapping path="/mini-app/**"/>
            <mvc:mapping path="/third/oauth/5i/redirect"/>
            <mvc:mapping path="/pay_bill/**"/>
            <mvc:mapping path="/bill/**"/>
            <mvc:mapping path="/qrcode_pay/**"/>
            <mvc:mapping path="/presell/**"/>
            <mvc:mapping path="/area/**"/>
            <mvc:mapping path="/call_service/**"/>
            <mvc:mapping path="/carts/**"/>
            <mvc:mapping path="/cart/**"/>
            <mvc:mapping path="/bind/**"/>
            <mvc:mapping path="/cash/**"/>
            <mvc:mapping path="/config/**"/>
            <mvc:mapping path="/enterprise/takeout/**"/>
            <mvc:mapping path="/enterprise/takeout/buytogether/v1/**"/>
            <mvc:mapping path="/evaluation/**"/>
            <mvc:mapping path="/order/**"/>
            <mvc:mapping path="/integral/**"/>
            <mvc:mapping path="/item/v1/**"/>
            <mvc:mapping path="/item/v2/**"/>
            <mvc:mapping path="/line_queue/**"/>
            <mvc:mapping path="/member/**"/>
            <mvc:mapping path="/cards/**"/>
            <mvc:mapping path="/orders/**"/>
            <mvc:mapping path="/pay/**"/>
            <mvc:mapping path="/pre_carts/**"/>
            <mvc:mapping path="/prepay/**"/>
            <mvc:mapping path="/privilege/**"/>
            <mvc:mapping path="/query_pay/**"/>
            <mvc:mapping path="/retail_menus/**"/>
            <mvc:mapping path="/share/**"/>
            <mvc:mapping path="/shop/**"/>
            <mvc:mapping path="/shop_member/v1/**"/>
            <mvc:mapping path="/takeout_carts/**"/>
            <mvc:mapping path="/takeout_pay/**"/>
            <mvc:mapping path="/takeout_menus/**"/>
            <mvc:mapping path="/takeout_orders/**"/>
            <mvc:mapping path="/template/**"/>
            <mvc:mapping path="/user_center/**"/>
            <mvc:mapping path="/users/**"/>
            <mvc:mapping path="/evaluation/**"/>
            <mvc:mapping path="/initial/v1/**"/>
            <mvc:mapping path="/retail/**"/>
            <mvc:mapping path="/retail_order/**"/>
            <mvc:mapping path="/lg/order/**"/>
            <mvc:mapping path="/scan/**"/>
            <mvc:mapping path="/activity/wxcoupon/**"/>
            <mvc:mapping path="/refund/**"/>
            <mvc:mapping path="/wework/**"/>
            <mvc:mapping path="/vcard/**"/>
            <mvc:mapping path="/bytedance/business/**"/>
            <mvc:mapping path="/invitation_gift/**"/>
            <mvc:mapping path="/third_coupon/**"/>
            <mvc:mapping path="/user/new_center/**"/>
            <mvc:mapping path="/coupon/**"/>
            <mvc:mapping path="/distribution/**"/>
            <mvc:mapping path="/verify_code/**"/>
            <mvc:mapping path="/invoice/**"/>
            <mvc:mapping path="/auction/**"/>
            <mvc:mapping path="/tip/**"/>
            <mvc:mapping path="/user_home_page/**"/>
            <mvc:mapping path="/international_invoice/**"/>


            <mvc:exclude-mapping path="/adv/v1/platform_ad"/>
            <mvc:exclude-mapping path="/adv/v2/anonymous/ad/info"/>
            <mvc:exclude-mapping path="/presell/v1/get_presell_menu_view"/>
            <mvc:exclude-mapping path="/order/callback/**"/>
            <mvc:exclude-mapping path="/mall/aliminiprogram/oauth/callback"/>
            <mvc:exclude-mapping path="/mall/aliminiprogram/message/notify"/>
            <mvc:exclude-mapping path="/cp_wechat/third/**"/>
            <mvc:exclude-mapping path="/alipay_card/**"/>
            <mvc:exclude-mapping path="/member/address/v1/is_in_range"/>
            <mvc:exclude-mapping path="/shop/v1/business/nearby_shops"/>
            <mvc:exclude-mapping path="/shop/v1/business/nearby_shops/count"/>
            <mvc:exclude-mapping path="/shop/v1/tude/judge"/>
            <mvc:exclude-mapping path="/shop/v1/countries"/>
            <mvc:exclude-mapping path="/shop/v1/distance/judge"/>
            <mvc:exclude-mapping path="/shop/v1/customer_service/list"/>
            <mvc:exclude-mapping path="/share/v1/business"/>
            <mvc:exclude-mapping path="/share/v1/get_jsapi_ticket"/>
            <mvc:exclude-mapping path="/share/v1/get_fan_jsapi_ticket"/>
            <mvc:exclude-mapping path="/share/v1/get_qq_jsapi_ticket"/>
            <mvc:exclude-mapping path="/enterprise/card/apply/bind_company_card"/>
            <mvc:exclude-mapping path="/mini-app/oauth/v1/login/**"/>
            <mvc:exclude-mapping path="/mini-app/oauth/v1/qy/login/**"/>
            <mvc:exclude-mapping path="/mini-app/oauth/v1/query_blacklist"/>
            <mvc:exclude-mapping path="/mini-app/oauth/v1/add_blacklist"/>
            <mvc:exclude-mapping path="/mini-app/users/v1/visitor/brand_publicity_info"/>
            <mvc:exclude-mapping path="/mini-app/jump/v1/noahpayRedirect"/>
            <mvc:exclude-mapping path="/mini-app/living/update_token"/>
            <mvc:exclude-mapping path="/presell/v1/dishs"/>
            <mvc:exclude-mapping path="/presell/v1/gotoAlbumDetail"/>
            <mvc:exclude-mapping path="/presell/v1/brand_list"/>
            <mvc:exclude-mapping path="/activity/wxcoupon/savewxcoupon"/>
            <mvc:exclude-mapping path="/query_pay/v1/getBillByMch"/>
            <mvc:exclude-mapping path="/config/v1/query_items"/>
            <mvc:exclude-mapping path="/config/v1/query_items"/>
            <mvc:exclude-mapping path="/distribution/v2/register_distributor"/>
            <mvc:exclude-mapping path="/distribution/v1/register_distributor"/>
            <mvc:exclude-mapping path="/verify_code/get_slide_code"/>
            <mvc:exclude-mapping path="/verify_code/get_verify_code"/>


            <bean class="com.twodfire.wechat.common.interceptor.SecurityAuthInterceptor"/>
        </mvc:interceptor>

        <!-- H5参数检查的拦截 -->
        <mvc:interceptor>
            <mvc:mapping path="/**"/>
            <bean class="com.twodfire.wechat.common.interceptor.ParamsCheckInterceptor"/>
        </mvc:interceptor>

        <!-- H5内嵌App支付模块的拦截 -->
        <mvc:interceptor>
            <mvc:mapping path="/cards/v1/establish_order"/>
            <mvc:mapping path="/cards/v1/get_pay_for_card"/>
            <mvc:mapping path="/cards/v1/recharge"/>
            <mvc:mapping path="/privilege/v1/get_coupon_order"/>
            <mvc:mapping path="/privilege/v1/get_pay_for_coupon"/>
            <mvc:mapping path="/pay/v1/get_pay_type"/>
            <mvc:mapping path="/takeout_orders/v1/get_pay_type"/>
            <bean class="com.twodfire.wechat.common.interceptor.H5EmbeddedAppPaymentInterceptor"/>
        </mvc:interceptor>

        <!-- 扫码、下单、支付 数据跟踪 -->
        <mvc:interceptor>
            <mvc:mapping path="/shop/v1/get_state"/>
            <mvc:mapping path="/initial/v1/**"/>
            <mvc:mapping path="/orders/v1/confirm"/>
            <mvc:mapping path="/takeout_orders/v1/confirm "/>
            <mvc:mapping path="/pay_bill/v1/pay_normal_bill"/>
            <mvc:mapping path="/pay_bill/v1/pay_pre_bill"/>
            <mvc:mapping path="/pay_bill/v1/pay_takeout_bill "/>
            <mvc:mapping path="/menus/v2/list"/>
            <mvc:mapping path="/order/get/v2/get_ordered"/>
            <bean class="com.twodfire.wechat.common.interceptor.UserTraceInterceptor"/>
        </mvc:interceptor>

        <!--风控（小二平台）黑名单-->
        <mvc:interceptor>
            <mvc:mapping path="/shop/v1/get_state"/>
            <mvc:mapping path="/initial/v1/**"/>
            <mvc:mapping path="/shop/info/v1/entity_info"/>
            <mvc:mapping path="/carts/v1/modify"/>
            <mvc:mapping path="/carts/v1/async_modify"/>
            <mvc:mapping path="/cards/v1/apply"/>
            <mvc:mapping path="/menus/v2/list"/>
            <mvc:mapping path="/menus/v1/normal_detail"/>
            <mvc:mapping path="/orders/v1/confirm"/>
            <mvc:mapping path="/bind/v1/get_code"/>
            <mvc:mapping path="/pre_carts/v1/modify"/>
            <mvc:mapping path="/pre_carts/v1/async_modify"/>
            <mvc:mapping path="/takeout_carts/v1/modify"/>
            <mvc:mapping path="/takeout_orders/v1/confirm"/>
            <bean class="com.twodfire.wechat.common.interceptor.ValidateGlobalBlackInterceptor"/>
        </mvc:interceptor>

        <!--风控（店铺维度）黑名单-->
        <mvc:interceptor>
            <mvc:mapping path="/activity/v1/activity_switch"/>
            <mvc:mapping path="/activity/v1/get_coin_trade_num"/>
            <mvc:mapping path="/lottery/v1/get_shop_info"/>
            <mvc:mapping path="/lottery/v1/get_one_buy_chance"/>
            <mvc:mapping path="/bill/v1/get_normal_trade_bill"/>
            <mvc:mapping path="/bill/v1/get_pre_trade_bill"/>
            <mvc:mapping path="/bill/v1/get_pay_other_trade_bill"/>
            <mvc:mapping path="/bill/v1/get_take_out_trade_bill"/>
            <mvc:mapping path="/bill/v1/get_app_bill_param"/>
            <mvc:mapping path="/carts/v1/create"/>
            <mvc:mapping path="/carts/v1/check_change"/>
            <mvc:mapping path="/carts/v1/clear"/>
            <mvc:mapping path="/carts/clear_by_id"/>
            <mvc:mapping path="/carts/get_recommend_menu_list"/>
            <mvc:mapping path="/carts/get_recommend_menu_by_id"/>
            <mvc:mapping path="/carts/must_modify"/>
            <mvc:mapping path="/integral/v1/integral_home_list"/>
            <mvc:mapping path="/integral/v1/integral_rule_list"/>
            <mvc:mapping path="/integral/v1/get_integral_coupon"/>
            <mvc:mapping path="/integral/v1/integral_exchange_coupon"/>
            <mvc:mapping path="/integral/v1/get_integral_card"/>
            <mvc:mapping path="/integral/v1/integral_exchange_card"/>
            <mvc:mapping path="/integral/v1/integral_exchange_list"/>
            <mvc:mapping path="/integral/v1/rechanger_send_coupons"/>
            <mvc:mapping path="/order/get/v2/get_recommend_menu_by_id"/>
            <mvc:mapping path="/pre_carts/v1/modify"/>
            <mvc:mapping path="/pre_carts/v1/async_modify"/>
            <mvc:mapping path="/privilege/v1/coupon/list"/>
            <mvc:mapping path="/privilege/v1/get_my_coupon_list"/>
            <mvc:mapping path="/privilege/v1/activity/list"/>
            <mvc:mapping path="/privilege/v2/activity/list"/>
            <mvc:mapping path="/privilege/v1/list"/>
            <mvc:mapping path="/privilege/v1/coupon/fetch"/>
            <mvc:mapping path="/privilege/v1/cart/list"/>
            <mvc:mapping path="/privilege/v1/takeout_cart/list"/>
            <mvc:mapping path="/privilege/v1/koubei/fetch"/>
            <mvc:mapping path="/privilege/v1/mini/coupon_page"/>
            <mvc:mapping path="/privilege/v1/mini/coupon_detail"/>
            <mvc:mapping path="/privilege/v1/mini/fetch"/>
            <mvc:mapping path="/privilege/v1/invalid/list"/>
            <mvc:mapping path="/privilege/v1/coupon/coupon_detail"/>
            <mvc:mapping path="/takeout_carts/v1/modify"/>
            <mvc:mapping path="/takeout_carts/v1/async_modify"/>
            <!--风控黑名单=店铺维度重合-->
            <mvc:mapping path="/shop/v1/get_state"/>
            <mvc:mapping path="/carts/v1/modify"/>
            <mvc:mapping path="/carts/v1/async_modify"/>
            <mvc:mapping path="/orders/v1/confirm"/>
            <bean class="com.twodfire.wechat.common.interceptor.ValidateBlackInterceptor"/>
        </mvc:interceptor>

        <!-- 业务埋点拦截器 -->
        <mvc:interceptor>
            <mvc:mapping path="/initial/v1/get_state_for_here"/>
            <mvc:mapping path="/menus/v2/list"/>
            <mvc:mapping path="/call_service/v1/call/{type}"/>
            <mvc:mapping path="/menus/v1/normal_detail"/>
            <mvc:mapping path="/template/v1/shop_template_list"/>
            <mvc:mapping path="/orders/v1/get_history_order"/>
            <mvc:mapping path="/pre_carts/v1/get_user_cart"/>
            <mvc:mapping path="/carts/v1/list"/>
            <mvc:mapping path="/carts/v1/clear"/>
            <mvc:mapping path="/orders/v1/confirm"/>
            <mvc:mapping path="/bill/v1/get_pre_trade_bill"/>
            <mvc:mapping path="/bill/v1/get_normal_trade_bill"/>
            <mvc:mapping path="/query_pay/bill"/>
            <mvc:mapping path="/order/get/v2/get_ordered"/>
            <mvc:mapping path="/order/get/v2/get_my_order_detail"/>
            <mvc:mapping path="/shop/info/v1/customer_comment"/>
            <mvc:mapping path="/user_center/v1/dash_board_second"/>
            <mvc:mapping path="/cards/v1/payment"/>
            <mvc:mapping path="/cards/v1/get_balance_detail_list"/>
            <mvc:mapping path="/privilege/v1/my_coupon_package/list"/>
            <mvc:mapping path="/privilege/v1/coupon/coupon_detail"/>
            <mvc:mapping path="/cards/v1/get_degree_detail_list"/>
            <mvc:mapping path="/cards/v1/get_my_card_list"/>
            <mvc:mapping path="/member/address/v1/get_user_address_list"/>
            <mvc:mapping path="/privilege/v1/list"/>
            <mvc:mapping path="/privilege/v1/activity/list"/>
            <mvc:mapping path="/shop/info/v1/p0"/>
            <bean class="com.twodfire.wechat.common.interceptor.MarkingLogInterceptor"/>
        </mvc:interceptor>

        <!-- 接口域名拦截器 -->
        <mvc:interceptor>
            <mvc:mapping path="/takeout_orders/v1/confirm"/>
            <bean class="com.twodfire.wechat.common.interceptor.DomainInterceptor"/>
        </mvc:interceptor>

    </mvc:interceptors>

<!--    <mvc:annotation-driven>-->
<!--        <mvc:message-converters>-->
<!--            <bean class="com.dfire.soa.multi.converter.MultiLanguageExtendsJacksonConverter">-->
<!--                <property name="objectMapper">-->
<!--                    <bean class="com.fasterxml.jackson.databind.ObjectMapper">-->
<!--                        &lt;!&ndash; 处理responseBody 里面日期类型 &ndash;&gt;-->
<!--                        <property name="dateFormat">-->
<!--                            <bean class="java.text.SimpleDateFormat">-->
<!--                                <constructor-arg type="java.lang.String" value="yyyy-MM-dd HH:mm:ss"/>-->
<!--                            </bean>-->
<!--                        </property>-->
<!--                        &lt;!&ndash; 为null字段时不显示 &ndash;&gt;-->
<!--                        <property name="serializationInclusion">-->
<!--                            <value type="com.fasterxml.jackson.annotation.JsonInclude.Include">NON_NULL</value>-->
<!--                        </property>-->
<!--                    </bean>-->
<!--                </property>-->
<!--            </bean>-->
<!--            <bean class="com.dfire.soa.multi.converter.MultiLanguageExtendsJacksonConverter">-->
<!--                <property name="objectMapper">-->
<!--                    <bean class="com.twodfire.wechat.common.conf.LongObjectMapper">-->
<!--                    </bean>-->
<!--                </property>-->
<!--            </bean>-->
<!--        </mvc:message-converters>-->
<!--    </mvc:annotation-driven>-->

    <mvc:annotation-driven>
        <mvc:message-converters>
            <bean class="com.twodfire.wechat.converter.CustomMultiLanguageExtendsJacksonConverter">
                <property name="objectMapper" ref="objectMapper"/>
                <!--不需要国际化的路径配置在这里-->
                <property name="excludeMultiContentPath">
                    <list>

                        <value type="java.lang.String">/query_multi_content</value>
                        <value>/get_slide_code</value>
                    </list>
                </property>
            </bean>
<!--                        <bean class="com.dfire.soa.multi.converter.MultiLanguageExtendsJacksonConverter">-->
<!--                            <property name="objectMapper">-->
<!--                                <bean class="com.twodfire.wechat.common.conf.LongObjectMapper">-->
<!--                                </bean>-->
<!--                            </property>-->
<!--                        </bean>-->
        </mvc:message-converters>
    </mvc:annotation-driven>

    <bean id="objectMapper" class="com.fasterxml.jackson.databind.ObjectMapper">
        <property name="serializationInclusion" value="NON_NULL"/>
        <property name="dateFormat">
            <bean class="java.text.SimpleDateFormat">
                <constructor-arg type="java.lang.String" value="yyyy-MM-dd HH:mm:ss"/>
            </bean>
        </property>
    </bean>

    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="objectMapper"/>
        <property name="targetMethod" value="configure"/>
        <property name="arguments">
            <list>
                <value type="com.fasterxml.jackson.databind.DeserializationFeature">FAIL_ON_UNKNOWN_PROPERTIES</value>
                <value>false</value>
            </list>
        </property>
    </bean>

    <bean id="viewResolver" class="org.springframework.web.servlet.view.InternalResourceViewResolver">
        <property name="prefix">
            <value>/WEB-INF/jsp/</value>
        </property>
        <property name="suffix">
            <value>.jsp</value>
        </property>
    </bean>

    <!--REST 异常处理-->
    <bean id="compositeExceptionResolver"
          class="org.springframework.web.servlet.handler.HandlerExceptionResolverComposite">
        <property name="order" value="0"/>
        <property name="exceptionResolvers">
            <list>
                <ref bean="exceptionHandlerExceptionResolver"/>
                <ref bean="restExceptionResolver"/>
            </list>
        </property>
    </bean>

    <bean id="restExceptionResolver"
          class="cz.jirutka.spring.exhandler.RestHandlerExceptionResolverFactoryBean">
        <property name="messageSource" ref="httpErrorMessageSource"/>
        <property name="defaultContentType" value="application/json"/>
        <property name="exceptionHandlers">
            <map>
                <entry key="org.springframework.dao.EmptyResultDataAccessException" value="404"/>
            </map>
        </property>
        <property name="httpMessageConverters">
            <bean class="com.dfire.soa.multi.converter.MultiLanguageExtendsJacksonConverter">
                <property name="objectMapper">
                    <bean class="com.fasterxml.jackson.databind.ObjectMapper">
                        <!-- 处理responseBody 里面日期类型 -->
                        <property name="dateFormat">
                            <bean class="java.text.SimpleDateFormat">
                                <constructor-arg type="java.lang.String" value="yyyy-MM-dd HH:mm:ss"/>
                            </bean>
                        </property>
                        <!-- 为null字段时不显示 -->
                        <property name="serializationInclusion">
                            <value type="com.fasterxml.jackson.annotation.JsonInclude.Include">NON_NULL</value>
                        </property>
                    </bean>
                </property>
            </bean>
        </property>
    </bean>

    <bean id="exceptionHandlerExceptionResolver"
          class="org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver"/>

    <bean id="httpErrorMessageSource"
          class="org.springframework.context.support.ReloadableResourceBundleMessageSource"
          p:basename="classpath:/errorMessages"
          p:defaultEncoding="UTF-8"/>


</beans>
