package com.twodfire.wechat.api;

import com.dfire.soa.consumer.param.CartItem;
import com.google.gson.Gson;
import org.junit.Before;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.test.web.servlet.setup.StandaloneMockMvcBuilder;
import org.testng.annotations.Test;

import java.util.ArrayList;
import java.util.List;

import static org.hamcrest.core.Is.is;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;


/**
 * Created by qiezi on 2015/11/19.
 */
public class PrePayControllerTest extends BaseControllerTestHelper {

    @Before
    public void init() {
        StandaloneMockMvcBuilder standaloneMockMvcBuilder = MockMvcBuilders.standaloneSetup(prePayController);
        standaloneMockMvcBuilder.addInterceptors(securityAuthInterceptor);
        this.mockMvc = standaloneMockMvcBuilder.build();
    }

    @Test
    public void testConfirmPrepayOrder() throws Exception {

        try {
            CartItem cartForms = new CartItem();
            cartForms.setMenuId("c8ec3c9582bb4d96a86f7ef057cb4709");
            cartForms.setNum(12.0);
            cartForms.setUid("b379d59ed79c4991a05fb2eb2a7b1c2c");
            cartForms.setMenuName("test_for_menu");
            cartForms.setMakeId("test_makeId");
            cartForms.setSpecId("test_for_SpecId");
            cartForms.setAddPriceMode(0);
            cartForms.setAddPrice(0.0);
            cartForms.setKindType(1);

//            List<CartItem> childCartVos = new ArrayList<>();
//
//            CartItem childCart = new CartItem();
//            childCart.setMenuId("000013314c7d25ca014c91cd87b800a0");
//            childCart.setNum(1d);
//            childCart.setMakeId("");
//            childCart.setSpecId("");
//
//            childCartVos.add(childCart);
//
//            childCart = new CartItem();
//            childCart.setMenuId("000013314c7d25ca014c91cd87760091");
//            childCart.setNum(1d);
//            childCart.setMakeId("");
//            childCart.setSpecId("");
//
//            childCartVos.add(childCart);
//
//            childCart = new CartItem();
//            childCart.setMenuId("000013314c7d25ca014c91cd87fb00af");
//            childCart.setNum(1d);
//            childCart.setMakeId("");
//            childCart.setSpecId("");

//            CartItem childCart = new CartItem();
//            childCart.setMenuId("00225105523e184601523fb08dab0073");
//            childCart.setNum(1d);
//            childCart.setMakeId("");
//
//            childCartVos.add(childCart);
//
//            childCart = new CartItem();
//            childCart.setMenuId("00225105523e184601523fb08e520075");
//            childCart.setNum(1d);
//            childCart.setMakeId("");
//
//            childCartVos.add(childCart);
//
//            childCart = new CartItem();
//            childCart.setMenuId("00225105523e184601523fb08f5b0078");
//            childCart.setNum(1d);
//            childCart.setMakeId("");

//            childCartVos.add(childCart);
//
//            cartForms.setChildCartVos(childCartVos);

            List<CartItem> CartItems = new ArrayList<>();
            CartItems.add(cartForms);

            String jsonStr = new Gson().toJson(CartItems);

            mockMvc.perform(post("/prepay/v1/confirm_prepay")
                    .param("entity_id", "99927792")
                    .param("seat_code", "B1")
                    .header("x-auth-token", "54452c7d6322ff78056424b22666bd1b")
                    .characterEncoding("UTF-8").accept(APPLICATION_JSON_UTF8)
                    .contentType(APPLICATION_JSON_UTF8)
                    .content(jsonStr))
                    .andExpect(status().isOk())
                    .andExpect(content().contentType(APPLICATION_JSON_UTF8))
//                    .andExpect(jsonPath("$.code", is("1")))
                    .andDo(MockMvcResultHandlers.print())
                    .andReturn().getResponse().getContentAsString();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testDeletePrepayOrder() throws Exception {
        String waiting_order_id = "00001331511f142a01511f142add0000";
        mockMvc.perform(post("/prepay/v1/delete/prepay_order?waiting_order_id={waitingOrderId}", waiting_order_id)
                .header("x-auth-token", TOKEN)
                .accept(APPLICATION_JSON_UTF8)
                .contentType(APPLICATION_JSON_UTF8))
                .andExpect(status().isOk())
                .andExpect(content().contentType(APPLICATION_JSON_UTF8))
//                .andExpect(jsonPath("$.code", is("1")))
                .andReturn().getResponse().getContentAsString();
    }

    @Test
    public void testGetPrepayOrder() throws Exception {
        mockMvc.perform(post("/prepay/v1/get_prepay_order?" +
                "entity_id={entityId}&seat_code={seatCode}", entityId, "")
                .header("x-auth-token", "37BB599E7EF2AF1BD9C3746FFC66A0DBE85875162FC8772CB59CAEE1215D40E2")
                .accept(APPLICATION_JSON_UTF8)
                .contentType(APPLICATION_JSON_UTF8))
                .andExpect(status().isOk())
                .andExpect(content().contentType(APPLICATION_JSON_UTF8))
                .andExpect(jsonPath("$.code", is("1"))).andDo(MockMvcResultHandlers.print())
                .andReturn().getResponse().getContentAsString();
    }

    @Test
    public void testGetPrepayInfo() throws Exception {
        mockMvc.perform(post("/prepay/v1/get_bill_info")
                .param("entity_id", entityId)
//                .param("seat_code", seatCode)
                .param("order_id", "00001331511f142a01511f142add0000")
                .header("x-auth-token", TOKEN)
                .accept(APPLICATION_JSON_UTF8)
                .contentType(APPLICATION_JSON_UTF8))
                .andExpect(status().isOk())
                .andExpect(content().contentType(APPLICATION_JSON_UTF8))
                .andExpect(jsonPath("$.code", is("1")))
                .andReturn().getResponse().getContentAsString();
//        prePayController.getPrepayInfo(entityId,seatCode,"");
    }

    @Test
    public void testDeletePrepay2() throws Exception {
        mockMvc.perform(post("/prepay/v1/delete/prepay_order")
                .param("waiting_order_id", "00001331511f142a01511f142add0000")
                .header("x-auth-token", TOKEN)
                .accept(APPLICATION_JSON_UTF8)
                .contentType(APPLICATION_JSON_UTF8))
                .andExpect(status().isOk())
                .andExpect(content().contentType(APPLICATION_JSON_UTF8))
//                .andExpect(jsonPath("$.code", is("1")))
                .andReturn().getResponse().getContentAsString();
    }

    public void testLockPayWaitingOrder() throws Exception {
        mockMvc.perform(post("/prepay/v1/lock_prepay_order")
                .param("waiting_order_id", "00001331511f142a01511f142add0000")
                .param("pay_money", "1")
                .header("x-auth-token", TOKEN)
                .accept(APPLICATION_JSON_UTF8)
                .contentType(APPLICATION_JSON_UTF8))
                .andExpect(status().isOk())
                .andExpect(content().contentType(APPLICATION_JSON_UTF8))
                .andExpect(jsonPath("$.code", is("1")))
                .andReturn().getResponse().getContentAsString();
    }

}
