package com.twodfire.wechat.api;

import com.twodfire.wechat.api.activity.ActivityController;
import com.twodfire.wechat.api.camera.CameraScanController;
import com.twodfire.wechat.api.cart.CartController;
import com.twodfire.wechat.api.cart.PreCartController;
import com.twodfire.wechat.api.cart.TakeoutCartController;
import com.twodfire.wechat.api.menu.MenusController;
import com.twodfire.wechat.api.oauth.OAuthController;
import com.twodfire.wechat.common.interceptor.SecurityAuthInterceptor;
import com.twodfire.wechat.common.interceptor.StopWatchLoggerInterceptor;
import org.junit.FixMethodOrder;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.testng.AbstractTestNGSpringContextTests;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;

import java.nio.charset.Charset;

/**
 * User: robin
 * Email: <EMAIL>
 * Date: 15/6/1
 * Time: PM2:15
 * Desc:
 */
@ContextConfiguration(locations = {"classpath:wechat-servlet.xml", "classpath:applicationContext.xml"})
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public abstract class BaseControllerTestHelper extends AbstractTestNGSpringContextTests {

    protected MockMvc mockMvc;
    @Autowired
    ThirdMemberCardController thirdMemberCardController;
    @Autowired
    MenusController menusController;
    @Autowired
    CartController cartController;
    @Autowired
    TakeoutCartController takeoutCartController;
    @Autowired
    TakeoutMenusController takeoutMenusController;
    @Autowired
    TakeoutPayController takeoutPayController;
    @Autowired
    OrderController orderController;
    @Autowired
    OrderGetController orderGetController;
    @Autowired
    UserController userController;
    @Autowired
    MemberCardController memberCardController;
    @Autowired
    ShopController shopController;
    //    @Autowired
//    CallServiceController callServiceController;
    @Autowired
    ShareController shareController;
    @Autowired
    PreCartController preCartController;
    @Autowired
    PrePayController prePayController;
    @Autowired
    AreaController areaController;
    @Autowired
    SecurityAuthInterceptor securityAuthInterceptor;
    @Autowired
    StopWatchLoggerInterceptor stopWatchLoggerInterceptor;
    //    @Autowired
//    CallServiceController callServiceV2Controller;
    @Autowired
    EvaluationController evaluationController;
    @Autowired
    BindingController bindingController;
    @Autowired
    QueryPayController queryPayController;
    @Autowired
    ActivityController activityController;
    @Autowired
    PayController payController;
    @Autowired
    TakeOutOrderController takeOutOrderController;
    @Autowired
    PrivilegeController privilegeController;
    @Autowired
    TakeOutController takeOutController;
    @Autowired
    TemplateController templateController;
    @Autowired
    OAuthController oAuthController;
    @Autowired
    ShopMemberController shopMemberController;
    @Autowired
    LineQueueController lineQueueController;
    @Autowired
    ShopInfoController shopInfoController;
    @Autowired
    UserCenterController userCenterController;
    @Autowired
    CameraScanController cameraScanController;

    //    @Autowired
//    ApiMonitorController apiMonitorController;
    public static final String TOKEN = "17f45e812809e64ecf31eb9ffc166d4c";//E928ECE643440421605AF99B79F369959C286870DBAF8BFC51772A5412F31E49//58e8b75b2123b8388dfa6fd532d7d6c3 oSqRbuOcT_Ojb6T_0oFGfrNFJ0O8 40afdf41afd2db7188b6fceb98d26f46
    //菜品ID
    public static final String MENUS_ID = "000013314c7d25ca014c91cd87290082"; //啤酒：001802745142ec0e015157c8197107f9 热菜：001802745142ec0e015157c81a4c0818 糖水：001802745142ec0e015157c82218092c  //热菜：000013314c7d25ca014c91cd85ed003c //啤酒：000013314d570dc0014d574771290040 //套菜: 0000133151a42be30151a47dc70d0005,0022510552edda4e0152f358e3740078

    public static final double NUM = 60.0d;
    //菜品名称
    public static final String MENUS_NAME = "test_for_menu";

    public static final String MAKE_ID = "test_makeId";

    public static final String specDetailId = "test_for_SpecId";

    public static final int addPriceMode = 0;

    public static final double addPrice = 0.0d;

    public static final int kindType = 1;    //用户ID
    public static final String UID = "e465b2cf432b455f87a05cd463696d8f";
    //数量
    public static final String NUMBER = "3";
    //服务类型
    public static final String SERVICE_TYPE = "food";

    /**
     * *********************************
     * shopId   店铺ID
     * entityId 店铺      1115
     * seatCode 桌位号    1
     * signKey  签名
     * *********************************
     */
//    public static final String shopId = "849fa0a3e9a748ed5bcebf1501a84cc6";
//    public static final String entityId = "00001115";
    public static final String shopId = "8ca2ebc86617ba17abdb184d73d78e95";
    public static final String entityId = "99001331";// 00180274,00000174,99001331,00225105,99180079
    public static final String seatCode = "B3";// C9,C5


    public static final String state = "1|||";
    public static final String signKey = "0a375419650669b7abb460c30890544c";
    public static final int page = 1;
    public static final int pageSize = 3;
    //萌萌达的薯条
    public static final String unionid = "oSqRbuOcT_Ojb6T_0oFGfrNFJ0O8";//oSqRbuFw5tZ-HdehwetoBpu0N8ns  oSqRbuOcT_Ojb6T_0oFGfrNFJ0O8
    public static final String redirect = "http://www.baidu.com";
    //open_id
    public static final String OPEN_ID = "o4KUGt9-xeMFes4F8wZlR6klpl7M";

    public static final MediaType APPLICATION_JSON_UTF8 = new MediaType(MediaType.APPLICATION_JSON.getType(),
            MediaType.APPLICATION_JSON.getSubtype(),
            Charset.forName("UTF-8")
    );


}
