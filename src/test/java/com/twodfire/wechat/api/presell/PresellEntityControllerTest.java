package com.twodfire.wechat.api.presell;

import com.twodfire.wechat.api.BaseControllerTestHelper;
import org.junit.Before;
import org.junit.Test;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.test.web.servlet.setup.StandaloneMockMvcBuilder;

import javax.annotation.Resource;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * PresellEntityControllerTest
 *
 * <AUTHOR>
 * @since 2018-07-06
 */
public class PresellEntityControllerTest extends BaseControllerTestHelper {
    
    @Resource
    private PresellEntityController presellEntityController;
    
    @Before
    public void init() {
        StandaloneMockMvcBuilder standaloneMockMvcBuilder = MockMvcBuilders.standaloneSetup(presellEntityController);
        // standaloneMockMvcBuilder.addInterceptors(securityAuthInterceptor);
        this.mockMvc = standaloneMockMvcBuilder.build();
    }
    
    @Test
    public void testEntityIndexDiscountPreview() throws Exception {
        mockMvc.perform(get("/presell/v2/entity_index_discount").param("entity_id", "99932333").
                header("x-auth-token", "68DD46DC23C0D99B81848387211648C83B88668F45A2B23E44EFFF87F3CB4090").characterEncoding("UTF-8").accept(APPLICATION_JSON_UTF8).contentType(APPLICATION_JSON_UTF8)).andExpect(status().isOk()).andExpect(content().contentType(APPLICATION_JSON_UTF8)).andDo(MockMvcResultHandlers.print()).andReturn();
    }
}
