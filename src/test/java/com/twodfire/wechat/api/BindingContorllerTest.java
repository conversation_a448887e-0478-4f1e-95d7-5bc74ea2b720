package com.twodfire.wechat.api;

import org.junit.Before;
import org.junit.Test;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.test.web.servlet.setup.StandaloneMockMvcBuilder;

import static org.hamcrest.core.Is.is;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * User:huangtao
 * Date:2016-03-16
 * description：
 */
public class BindingContorllerTest extends BaseControllerTestHelper {
    @Before
    public void init() {
        StandaloneMockMvcBuilder standaloneMockMvcBuilder = MockMvcBuilders.standaloneSetup(bindingController);
        standaloneMockMvcBuilder.addInterceptors(securityAuthInterceptor);
        this.mockMvc = standaloneMockMvcBuilder.build();
    }

    @Test
    public void testSendVerifyCode() throws Exception {
        mockMvc.perform(post("/bind/v1/get_code")
                .param("mobile", "15100000010")
                .param("area_code", "+86")
                .header("x-auth-token", "6963cd28d88094340db0d048eeb5253f"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(APPLICATION_JSON_UTF8));
    }

    /**
     * 发送语言验证码方法的单元测试
     *
     * @throws Exception 业务异常
     */
    @Test
    public void testSendVoiceVerifyCode() throws Exception {
        mockMvc.perform(post("/bind/v1/get_voice_code")
                .param("mobile", "18843143073")
                .param("area_code", "+86")
                .header("x-auth-token", "6963cd28d88094340db0d048eeb5253f"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(APPLICATION_JSON_UTF8));
    }


    @Test
    public void testVerifyCode() throws Exception {
        mockMvc.perform(post("/bind/v1/verify")
                .param("mobile", "15100000010")
                .param("area_code", "+86")
                .param("code", "2736")
                .param("entity_id", "99001331")
                .header("x-auth-token", "4137f18af696bf23a858cff799d3360a"))
//                .andExpect(status().isOk())
                .andExpect(content().contentType(APPLICATION_JSON_UTF8))
                .andDo(MockMvcResultHandlers.print());
//                .andExpect(jsonPath("$.code", is("1")));

        mockMvc.perform(post("/bind/v1/verify")
                .param("mobile", "15100000010")
                .param("area_code", "+86")
                .param("code", "2736")
                .param("entity_id", "99001331")

                .header("x-auth-token", "4137f18af696bf23a858cff799d3360a"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(APPLICATION_JSON_UTF8))
                .andDo(MockMvcResultHandlers.print());
//                .andExpect(jsonPath("$.code", is("1")));

        mockMvc.perform(post("/bind/v1/verify")
                .param("mobile", "15100000010")
                .param("area_code", "+86")
                .param("code", "2736")
                .param("entity_id", "99001331")
                .header("x-auth-token", "4137f18af696bf23a858cff799d3360a"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(APPLICATION_JSON_UTF8))
                .andDo(MockMvcResultHandlers.print());
//                .andExpect(jsonPath("$.code", is("1")));
    }

    @Test
    public void testBingTimer() throws Exception {
//        mockMvc.perform(post("/bind/v1/bing_timer").param("mobile", "15757115785")
//                .header("x-auth-token", TOKEN))
//                .andExpect(status().isOk())
//                .andExpect(content().contentType(APPLICATION_JSON_UTF8))
//                .andExpect(jsonPath("$.code", is("1")));
    }

    @Test
    public void testQrCode() throws Exception {
//        StandaloneMockMvcBuilder standaloneMockMvcBuilder = MockMvcBuilders.standaloneSetup(queryPayController);
//        standaloneMockMvcBuilder.addInterceptors(securityAuthInterceptor);
//        this.mockMvc = standaloneMockMvcBuilder.build();
//        mockMvc.perform(get("/query_pay/bill")
//                .param("entity_id", "99925625")
//                .param("out_trade_no", "999256255sixMcySAU58kSvT7P2D4b")
//                .param("pay_type", "1")
//                .header("x-auth-token", "37BB599E7EF2AF1BD9C3746FFC66A0DBE85875162FC8772CB59CAEE1215D40E2"))
//                .andExpect(status().isOk())
//                .andExpect(content().contentType(APPLICATION_JSON_UTF8))
//                .andExpect(jsonPath("$.code", is("1")));
    }
}
