package com.twodfire.wechat.facade;


import com.dfire.soa.thirdcrm.bo.ThirdBindBO;
import com.dfire.soa.thirdcrm.bo.ThirdCrmSystemBO;
import com.twodfire.share.result.Result;
import com.twodfire.wechat.api.BaseControllerTestHelper;
import com.twodfire.wechat.vo.card.ThirdCardStatusVo;
import com.twodfire.wechat.vo.form.ThirdCardBindForm;
import com.dfire.soa.thirdcrm.bo.BindResultBO;
import com.twodfire.wechat.vo.member.ThirdBindVO;
import com.twodfire.wechat.vo.member.ThirdCrmSystemVO;
import org.junit.Ignore;
import org.junit.Test;

import javax.annotation.Resource;

import java.util.List;


public class ThirdMemberCardFacadeTest extends BaseControllerTestHelper {

    @Resource
    private ThirdMemberCardFacade thirdMemberCardFacade;

    private static String entityId = "99929254";

    private static String customerId = "233281c09bf4422f8c15095e22fe4a07";

    private static String cardCode = "9140120623";

    private static String mobile = "15958011319";

    private static Long thirdCrmSystemId = 12L;

    private static String entityName = "浙江大学";

    private static String thirdCrmSystemName = "";

    private static Long id = 372154527295586900L;

    private static String kindCardName = "消费卡";

    private static String customerName = "严立强";
    ;

    @Test
    public void testGetIndustryType() {
    }

    @Test
    public void testQueryThirdCrmSystemList() {
        List<ThirdCrmSystemVO> thirdCrmSystemVOS = thirdMemberCardFacade.queryThirdCrmSystemList(entityId);
        for (ThirdCrmSystemVO ThirdCrmSystemVO : thirdCrmSystemVOS) {
            System.out.println(ThirdCrmSystemVO.toString());
        }
    }

    @Test
    public void testQueryBindList() {
        List<ThirdBindVO> thirdBindVOS = thirdMemberCardFacade.queryBindList(entityId, customerId);
        for(ThirdBindVO thirdBindVO : thirdBindVOS){
            System.out.println(thirdBindVO.toString());
        }

        /*Result<List<ThirdCrmSystemBO>> result = thirdMemberCardFacade.queryThirdCrmSystemList(entityId);
        for (ThirdCrmSystemBO ThirdCrmSystemBO : result.getModel()) {
            System.out.println(ThirdCrmSystemBO.toString());
        }*/
    }

    @Test
    @Ignore
    public void testBind() {
        ThirdCardBindForm thirdCardBindForm = new ThirdCardBindForm();
        thirdCardBindForm.setCustomerId(customerId);
        thirdCardBindForm.setCardCode(cardCode);
        thirdCardBindForm.setMobile(mobile);
        thirdCardBindForm.setOrgId(thirdCrmSystemId);
        boolean b = thirdMemberCardFacade.bindThirdCard(thirdCardBindForm);
        //success=true;
    }

    @Test
    @Ignore
    public void testUpdate() {
        ThirdCardBindForm thirdCardBindForm = new ThirdCardBindForm();
        thirdCardBindForm.setCustomerId(customerId);
        thirdCardBindForm.setCardCode(cardCode);
        thirdCardBindForm.setMobile(mobile);
        //thirdCardBindForm.setThirdCrmSystemId(thirdCrmSystemId);
        thirdCardBindForm.setOrgId(thirdCrmSystemId);
        boolean b = thirdMemberCardFacade.bindThirdCard(thirdCardBindForm);
        System.out.println(b);
    }

    @Test
    public void testDelete() {
        boolean b = thirdMemberCardFacade.deleteThirdCard(id, customerId);
        System.out.println(b);
        //success=true;
    }


    @Test
    public void testGetThirdCardOpenStatus() {
        System.out.println(thirdMemberCardFacade);
        ThirdCardStatusVo thirdCardOpenStatus = thirdMemberCardFacade.getThirdCardOpenStatus("99929254", "0b62795bc3f54f039d717296bb097867");
        System.out.println(thirdCardOpenStatus.getThirdCardOpenStatus() + "" + thirdCardOpenStatus.getThirdCardType());
    }
}