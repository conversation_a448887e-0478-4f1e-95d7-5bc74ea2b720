<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>parent-all</artifactId>
        <groupId>com.dfire</groupId>
        <version>1.0.3</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.twodfire.wechat</groupId>
    <artifactId>online-wechat</artifactId>
    <version>3.2.3</version>
    <packaging>war</packaging>

    <properties>
        <org.springframework-version>4.3.11.RELEASE</org.springframework-version>
        <org.aspectj-version>1.6.10</org.aspectj-version>
        <org.slf4j-version>1.7.7</org.slf4j-version>
        <logback-version>1.1.3</logback-version>
        <jackson.version>2.18.1</jackson.version>
        <member-client-version>1.2.0</member-client-version>
        <twodfire-remote-version>1.2.32</twodfire-remote-version>
        <maven.build.timestamp.format>yyyyMMddHHmmss</maven.build.timestamp.format>
        <timestampre>${maven.build.timestamp}</timestampre>
        <control-client-version>1.1.7</control-client-version>
        <shop-client.version>1.3.64</shop-client.version>
        <waiter-version>1.0.14</waiter-version>
        <twodfire-swagger>1.0.6</twodfire-swagger>
        <order-client-version>3.7.23</order-client-version>
        <order-search-client-version>1.1.79</order-search-client-version>
        <matrix-client.version>1.3.6</matrix-client.version>
        <item-platform.version>1.4.45</item-platform.version>
        <pay-client.version>1.2.92</pay-client.version>
        <consumer-client.version>2.7.40</consumer-client.version>
        <turtle-client.version>1.5.22</turtle-client.version>
        <dfire-util.version>1.1.13</dfire-util.version>
        <dfire-result.version>1.0.10</dfire-result.version>
        <disconf-client.version>2.6.35</disconf-client.version>
        <activity-client.version>1.3.7</activity-client.version>
        <boss.client.version>1.8.16</boss.client.version>
        <tc-client.version>2.5.31</tc-client.version>
        <biz-conf-client.version>1.1.83</biz-conf-client.version>
        <mc-client.version>1.2.90</mc-client.version>
        <msstate-client.version>1.0.29</msstate-client.version>
        <pay-center-client.version>1.1.68-RELEASE</pay-center-client.version>
        <interest-client.version>1.0.0</interest-client.version>
        <shop-member-client.version>1.1.88</shop-member-client.version>
        <cart-client.version>1.0.63</cart-client.version>
        <consumer-util.version>1.1.0</consumer-util.version>
        <oauth-client.version>1.5.99</oauth-client.version>
        <oauth-http-client.version>1.5.98</oauth-http-client.version>
        <oauth-center-international-client.version>1.0.2</oauth-center-international-client.version>
        <interest-client.version>1.0.0</interest-client.version>
        <i18n.version>1.1.40</i18n.version>
        <shopconf-client.version>1.1.92</shopconf-client.version>
        <bps-client>1.1.58</bps-client>
        <sm-client>1.3.7</sm-client>
        <wechat-client.version>1.5.38</wechat-client.version>
        <line-client>1.2.0</line-client>
        <trade-platform-version>2.4.27</trade-platform-version>
        <qrcode-client-version>1.3.40</qrcode-client-version>
        <sso-client-version>1.0.5</sso-client-version>
        <dubbo.version>2.6.5</dubbo.version>
        <twodfire-redis.version>1.5.4</twodfire-redis.version>
        <fm-client-version>1.0.34</fm-client-version>
        <wt-mp.version>1.5.52</wt-mp.version>
        <shop-center-client-version>1.8.34</shop-center-client-version>
        <card-center-client.version>1.1.8</card-center-client.version>
        <enterprise-client.version>1.0.27</enterprise-client.version>
        <cash-client-version>1.2.97</cash-client-version>
        <fe-client.version>1.0.4</fe-client.version>
        <takeout-user-client.version>1.2.99</takeout-user-client.version>
        <takeout-client.version>1.1.85</takeout-client.version>
        <cashstate-client-version>1.0.7</cashstate-client-version>
        <third-internal-client.version>1.1.57</third-internal-client.version>
        <consumer-market-client.version>1.0.15</consumer-market-client.version>
        <rest-adapter-client-version>1.2.95</rest-adapter-client-version>
        <multi-filter-version>1.1.9</multi-filter-version>
        <multi-client-version>1.0.33</multi-client-version>
        <multi-converter.version>1.0.13</multi-converter.version>
        <activity-platform-client.version>1.5.55</activity-platform-client.version>
        <fa-client.version>1.1.16</fa-client.version>
        <activity-center-client.version>1.0.97</activity-center-client.version>
        <twodfire-session-version>1.0.9</twodfire-session-version>
        <retail-adapter-client.version>1.0.1</retail-adapter-client.version>
        <complex-industry-client.version>1.0.77</complex-industry-client.version>
        <cm.client-version>1.3.13</cm.client-version>
        <refund-client-version>1.0.3</refund-client-version>
        <retail-common-client-version>1.0.11</retail-common-client-version>
        <fire-delivery-client-version>1.1.1</fire-delivery-client-version>
        <sentinel-version>0.2.0</sentinel-version>
        <third-crm-clien-version>1.0.10</third-crm-clien-version>
        <market-tool-version>1.1.7</market-tool-version>
        <inventory-client.version>1.0.18</inventory-client.version>
        <bj-train-adapter-client.version>1.0.1</bj-train-adapter-client.version>
        <validation-api-version>2.0.1.Final</validation-api-version>
        <market-agent-soa-client.version>1.0.1</market-agent-soa-client.version>
        <dmall-client-version>1.0.45</dmall-client-version>
        <hpp-platform-client.version>1.2.22</hpp-platform-client.version>
        <auditlog.version>1.0.5</auditlog.version>
        <merchant-client-version>1.0.69</merchant-client-version>
        <boss-center-client-version>1.5.45</boss-center-client-version>
        <express-client.version>1.1.9</express-client.version>
        <hpp-distribution-client.version>1.0.46</hpp-distribution-client.version>
        <reserve-client.version>1.2.14</reserve-client.version>
        <invoice-client.version>1.0.54</invoice-client.version>
        <bytedance-mp.version>1.0.0</bytedance-mp.version>
        <intelligence.version>1.1.45</intelligence.version>
        <fin-thirdpart-merchant-center-client.version>1.3.56-RELEASE</fin-thirdpart-merchant-center-client.version>
        <wework-scrm-client.version>1.0.6</wework-scrm-client.version>
        <lombok.version>1.18.20</lombok.version>
        <hutool.version>5.8.26</hutool.version>
        <seat-status.version>1.0.9</seat-status.version>
        <tc-conf-client.version>1.2.13</tc-conf-client.version>
        <dfire-I18n.version>1.0.5</dfire-I18n.version>
        <koubei-adapter-client.version>1.1.7</koubei-adapter-client.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.openai</groupId>
            <artifactId>openai-java</artifactId>
            <version>0.45.0</version>
        </dependency>

        <dependency>
            <groupId>com.squareup.okio</groupId>
            <artifactId>okio</artifactId>
            <version>3.6.0</version>
        </dependency>
        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>koubei-adapter-client</artifactId>
            <version>${koubei-adapter-client.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.zookeeper</groupId>
            <artifactId>zookeeper</artifactId>
            <version>3.4.6</version>
            <exclusions>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-validator</artifactId>
            <version>5.2.4.Final</version>
        </dependency>
        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>intelligence-client</artifactId>
            <version>${intelligence.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.dfire.soa</groupId>
                    <artifactId>item-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>express-client</artifactId>
            <version>${express-client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>promotion-platform-client</artifactId>
            <version>1.0.24</version>
        </dependency>
        <!-- 审计日志 -->
        <dependency>
            <groupId>com.dfire.platform</groupId>
            <artifactId>auditlog-client</artifactId>
            <version>${auditlog.version}</version>

        </dependency>


        <!--hpp-->
        <dependency>
            <groupId>com.dfire.soa.consumer</groupId>
            <artifactId>hpp-platform-client</artifactId>
            <version>${hpp-platform-client.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>pay-center-client</artifactId>
                    <groupId>com.dfire.pay.center</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>order-client</artifactId>
                    <groupId>com.dfire.soa</groupId>
                </exclusion>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.dfire.soa.hpp</groupId>
            <artifactId>hpp-distribution-client</artifactId>
            <version>${hpp-distribution-client.version}</version>
        </dependency>

        <dependency>
            <groupId>com.dfire.mmt.agent</groupId>
            <artifactId>market-agent-soa-client</artifactId>
            <version>${market-agent-soa-client.version}</version>
        </dependency>

        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>boss-center-client</artifactId>
            <version>${boss-center-client-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>i18n</artifactId>
                    <groupId>com.dfire</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>item-client</artifactId>
                    <groupId>com.dfire.soa</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>intelligence-client</artifactId>
                    <groupId>com.dfire.soa</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>order-client</artifactId>
                    <groupId>com.dfire.soa</groupId>
                </exclusion>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--第三方会员卡-->
        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>third-crm-client</artifactId>
            <version>${third-crm-clien-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>lombok</artifactId>
                    <groupId>org.projectlombok</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- market -->
        <dependency>
            <groupId>com.dfire.mmt</groupId>
            <artifactId>market-tool</artifactId>
            <version>${market-tool-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>validation-api</artifactId>
                    <groupId>javax.validation</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>dubbo</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--retail-->
        <dependency>
            <groupId>com.dfire.soa.complex</groupId>
            <artifactId>complex-industry-client</artifactId>
            <version>${complex-industry-client.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>i18n</artifactId>
                    <groupId>com.dfire</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>shopconf-client</artifactId>
                    <groupId>com.dfire.soa</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--inventory-->
        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>inventory-client</artifactId>
            <version>${inventory-client.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>item-client</artifactId>
                    <groupId>com.dfire.soa</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>item-platform-client</artifactId>
                    <groupId>com.dfire.soa</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>retail-adapter-client</artifactId>
            <version>${retail-adapter-client.version}</version>
        </dependency>

        <!--activity-center-->
        <dependency>
            <groupId>com.dfire.soa.mmt</groupId>
            <artifactId>activity-center-client</artifactId>
            <version>${activity-center-client.version}</version>
        </dependency>
        <!--fa-client-->
        <dependency>
            <groupId>com.dfire.soa.consumer.fa</groupId>
            <artifactId>fa-client</artifactId>
            <version>${fa-client.version}</version>
        </dependency>
        <!-- rest-adapter soa start -->
        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>rest-adapter-client</artifactId>
            <version>${rest-adapter-client-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>bd.sdk-client</artifactId>
                    <groupId>com.dfire.open.api</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>meituan-api</artifactId>
                    <groupId>com.meituan.waimai</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>eleme-openapi-sdk-mod</artifactId>
                    <groupId>me.ele.openapi</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>item-client</artifactId>
                    <groupId>com.dfire.soa</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>alipay-api</artifactId>
                    <groupId>com.alipay.koubei</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>order-client</artifactId>
                    <groupId>com.dfire.soa</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- rest-adapter soa end -->

        <!--采购平台-->
        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>dmall-client</artifactId>
            <version>${dmall-client-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>multi-client</artifactId>
                    <groupId>com.dfire.soa</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.dfire.consumer</groupId>
            <artifactId>activity-platform-client</artifactId>
            <version>${activity-platform-client.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>card-center-client</artifactId>
                    <groupId>com.dfire.soa</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>shop-member-client</artifactId>
                    <groupId>com.dfire.soa</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>multi-client</artifactId>
                    <groupId>com.dfire.soa</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>multi-filter</artifactId>
                    <groupId>com.dfire.soa</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dfire.consumer</groupId>
            <artifactId>consumer-market-client</artifactId>
            <version>${consumer-market-client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>cashstate-client</artifactId>
            <version>${cashstate-client-version}</version>
        </dependency>

        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>third-internal-client</artifactId>
            <version>${third-internal-client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>shop-center-client</artifactId>
            <version>${shop-center-client-version}</version>
        </dependency>
        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>shop-op-client</artifactId>
            <version>1.0.5</version>
        </dependency>
        <dependency>
            <groupId>com.dfire.soa.market</groupId>
            <artifactId>enterprise-client</artifactId>
            <version>${enterprise-client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dfire.soa.consumer</groupId>
            <artifactId>takeout-user-client</artifactId>
            <version>${takeout-user-client.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>validation-api</artifactId>
                    <groupId>javax.validation</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dfire.soa.consumer</groupId>
            <artifactId>fire-delivery-client</artifactId>
            <version>${fire-delivery-client-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>validation-api</artifactId>
                    <groupId>javax.validation</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dfire.soa.consumer</groupId>
            <artifactId>takeout-client</artifactId>
            <version>${takeout-client.version}</version>
        </dependency>
        <!-- fire member -->
        <dependency>
            <groupId>com.dfire.soa.consumer</groupId>
            <artifactId>fm-client</artifactId>
            <version>${fm-client-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>servlet-api</artifactId>
                    <groupId>javax.servlet</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>card-center-client</artifactId>
            <version>${card-center-client.version}</version>
        </dependency>
        <!-- sso client -->
        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>sso-client</artifactId>
            <version>${sso-client-version}</version>
        </dependency>

        <!-- tp client -->
        <dependency>
            <groupId>com.dfire.tp</groupId>
            <artifactId>tp-client</artifactId>
            <version>${trade-platform-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>cart-client</artifactId>
                    <groupId>com.dfire.soa.consumer</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>order-client</artifactId>
                    <groupId>com.dfire.soa</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>bps-client</artifactId>
            <version>${bps-client}</version>
            <exclusions>
                <exclusion>
                    <artifactId>common-validator</artifactId>
                    <groupId>com.dfire.middleware</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.twodfire</groupId>
            <artifactId>dfire-result</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>line-client</artifactId>
            <version>${line-client}</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--wechat-client-->
        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>wechat-client</artifactId>
            <version>${wechat-client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>shopconf-client</artifactId>
            <version>${shopconf-client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.dfire.mc</groupId>
                    <artifactId>mc-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>oauth-client</artifactId>
            <version>${oauth-client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>oauth-http-request</artifactId>
            <version>${oauth-http-client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dfire.soa.center.international</groupId>
            <artifactId>oauth-client</artifactId>
            <version>${oauth-center-international-client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dfire.consumer</groupId>
            <artifactId>consumer-util</artifactId>
            <version>${consumer-util.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>dubbo</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--shop-member-client-->
        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>shop-member-client</artifactId>
            <version>${shop-member-client.version}</version>
        </dependency>

        <!--interest-client-->
        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>interest-client</artifactId>
            <version>${interest-client.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>sm-client</artifactId>
            <version>${sm-client}</version>
            <exclusions>
                <exclusion>
                    <artifactId>common-validator</artifactId>
                    <groupId>com.dfire.middleware</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.dfire.soa.consumer</groupId>
            <artifactId>cart-client</artifactId>
            <version>${cart-client.version}</version>
        </dependency>

        <!-- 支付中心 -->
        <dependency>
            <groupId>com.dfire.pay.center</groupId>
            <artifactId>pay-center-client</artifactId>
            <version>${pay-center-client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.dfire.pay</groupId>
                    <artifactId>pay-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dfire.mc</groupId>
            <artifactId>mc-client</artifactId>
            <version>${mc-client.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>biz-conf-client</artifactId>
            <version>${biz-conf-client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-lang3</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>rerp-config-object</artifactId>
                    <groupId>com.dfire.soa</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.github.sgroschupf</groupId>
            <artifactId>zkclient</artifactId>
            <version>0.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--boss-->
        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>boss-client</artifactId>
            <version>${boss.client.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>boss-utils</artifactId>
                    <groupId>com.twodfire</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>item-client</artifactId>
                    <groupId>com.dfire.soa</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.baidu.disconf</groupId>
            <artifactId>disconf-client</artifactId>
            <version>${disconf-client.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>javassist</artifactId>
                    <groupId>org.javassist</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.twodfire</groupId>
            <artifactId>twodfire-logback</artifactId>
            <version>1.0.8</version>
        </dependency>
        <!-- 座位信息相关 -->
        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>turtle-client</artifactId>
            <version>${turtle-client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dfire.pay</groupId>
            <artifactId>pay-client</artifactId>
            <version>${pay-client.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--google工具-->
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>2.4</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>19.0-rc2</version>
        </dependency>

        <!--北京高铁-->
        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>bj-train-adapter-client</artifactId>
            <version>${bj-train-adapter-client.version}</version>
        </dependency>

        <!-- miniapp -->
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-miniapp</artifactId>
            <version>2.9.1-Final</version>
        </dependency>
        <dependency>
            <groupId>com.dfire.middleware</groupId>
            <artifactId>common-validator</artifactId>
            <version>1.1.0</version>
            <exclusions>
                <exclusion>
                    <groupId>net.sf.oval</groupId>
                    <artifactId>oval</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.aspectj</groupId>
                    <artifactId>aspectjrt</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-aspects</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>consumer-client</artifactId>
            <version>${consumer-client.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>item-client</artifactId>
                    <groupId>com.dfire.soa</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>item-platform-client</artifactId>
                    <groupId>com.dfire.soa</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>member-client</artifactId>
                    <groupId>com.dfire.soa</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>tp-client</artifactId>
                    <groupId>com.dfire.tp</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>shopconf-client</artifactId>
                    <groupId>com.dfire.soa</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>order-client</artifactId>
                    <groupId>com.dfire.soa</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--dubbo-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>dubbo</artifactId>
            <version>${dubbo.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>javassist</artifactId>
                    <groupId>org.javassist</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.dfire</groupId>
            <artifactId>dfire-dubbo</artifactId>
            <version>1.0.6</version>
        </dependency>


        <!--swagger-->
        <dependency>
            <groupId>com.twodfire</groupId>
            <artifactId>twodfire-swagger</artifactId>
            <version>${twodfire-swagger}</version>
            <exclusions>
                <exclusion>
                    <artifactId>spring-web</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>control-client</artifactId>
            <version>${control-client-version}</version>
        </dependency>

        <dependency>
            <artifactId>dfire-util</artifactId>
            <groupId>com.dfire</groupId>
        </dependency>

        <dependency>
            <groupId>commons-httpclient</groupId>
            <artifactId>commons-httpclient</artifactId>
            <version>3.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dfire.soa.waiter</groupId>
            <artifactId>waiter-client</artifactId>
            <version>${waiter-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>common-validator</artifactId>
                    <groupId>com.dfire.middleware</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>i18n</artifactId>
                    <groupId>com.dfire</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <dependency>
            <!-- Note: core-annotations version x.y.0 is generally compatible with
                 (identical to) version x.y.1, x.y.2, etc. -->
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>${jackson.version}</version>
        </dependency>

        <dependency>
            <groupId>com.twodfire</groupId>
            <artifactId>twodfire-remote</artifactId>
            <version>${twodfire-remote-version}</version>
        </dependency>
        <dependency>
            <groupId>com.twodfire</groupId>
            <artifactId>twodfire-redis</artifactId>
            <version>${twodfire-redis.version}</version>
        </dependency>
        <dependency>
            <groupId>io.codis.jodis</groupId>
            <artifactId>jodis</artifactId>
            <version>0.5.0</version>
        </dependency>

        <!--会员服务化-->
        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>member-client</artifactId>
            <version>${member-client-version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.zmsoft.manager</groupId>
                    <artifactId>com.zmsoft.zm.service</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.zmsoft.manager</groupId>
                    <artifactId>com.zmsoft.zm.zmbo</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--backstage-->
        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>backstage-client</artifactId>
            <version>1.0.3</version>
            <exclusions>
                <exclusion>
                    <artifactId>common-validator</artifactId>
                    <groupId>com.dfire.middleware</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 店铺服务 -->
        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>shop-client</artifactId>
            <version>${shop-client.version}</version>
        </dependency>

        <dependency>
            <groupId>com.dfire.tpt</groupId>
            <artifactId>tpt-utils</artifactId>
            <version>1.0.11-RELEASE</version>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--事件驱动插件-->
        <dependency>
            <groupId>com.dfire</groupId>
            <artifactId>event-plugin</artifactId>
            <version>1.0.1</version>
            <exclusions>
                <exclusion>
                    <groupId>com.lmax</groupId>
                    <artifactId>disruptor</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--订单服务化-->
        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>order-client</artifactId>
            <version>${order-client-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>order-search-client</artifactId>
            <version>${order-search-client-version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>order-client</artifactId>
                    <groupId>com.dfire.soa</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--msstate-client-->
        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>msstate-client</artifactId>
            <version>${msstate-client.version}</version>
        </dependency>

        <!-- 收银服务 -->
        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>cash-client</artifactId>
            <version>${cash-client-version}</version>
        </dependency>

        <!-- 商品服务 -->
        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>item-platform-client</artifactId>
            <version>${item-platform.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>matrix-client</artifactId>
            <version>${matrix-client.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--交易中心-->
        <dependency>
            <groupId>com.dfire.tc</groupId>
            <artifactId>tc-client</artifactId>
            <version>${tc-client.version}</version>
        </dependency>
        <dependency>
        <groupId>com.dfire.soa</groupId>
        <artifactId>fin-thirdpart-merchant-center-client</artifactId>
        <version>${fin-thirdpart-merchant-center-client.version}</version>
        </dependency>
        <!--点赞活动-->
        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>activity-client</artifactId>
            <version>${activity-client.version}</version>
        </dependency>
        <!--monitor 下-->
        <dependency>
            <groupId>com.dfire.shared</groupId>
            <artifactId>dfire-servlet-monitor</artifactId>
            <version>1.1.2</version>
        </dependency>
        <dependency>
            <groupId>com.dfire.servlet.monitor</groupId>
            <artifactId>app-manage</artifactId>
            <version>1.0.4</version>
        </dependency>
        <!--monitor 上 -->

        <dependency>
            <groupId>com.dfire.wt</groupId>
            <artifactId>wt-mp</artifactId>
            <version>${wt-mp.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.httpcomponents</groupId>
                    <artifactId>httpcore</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-io</artifactId>
                    <groupId>commons-io</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>gson</artifactId>
                    <groupId>com.google.code.gson</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
            <version>${org.springframework-version}</version>
            <exclusions>
                <!-- Exclude Commons Logging in favor of SLF4j -->
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
            <version>${org.springframework-version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
            <version>${org.springframework-version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aspects</artifactId>
            <version>${org.springframework-version}</version>
        </dependency>
        <!-- AspectJ -->
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjrt</artifactId>
            <version>${org.aspectj-version}</version>
        </dependency>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>${org.slf4j-version}</version>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>jcl-over-slf4j</artifactId>
            <version>${org.slf4j-version}</version>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>log4j-over-slf4j</artifactId>
            <version>${org.slf4j-version}</version>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
            <version>${logback-version}</version>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>${logback-version}</version>
        </dependency>
        <dependency>
            <groupId>janino</groupId>
            <artifactId>janino</artifactId>
            <version>2.5.10</version>
        </dependency>
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>4.4</version>
        </dependency>
        <!-- @Inject -->
        <dependency>
            <groupId>javax.inject</groupId>
            <artifactId>javax.inject</artifactId>
            <version>1</version>
        </dependency>
        <dependency>
            <groupId>org.javassist</groupId>
            <artifactId>javassist</artifactId>
            <version>3.21.0-GA</version>
        </dependency>
        <!-- mybatis -->
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
            <version>3.2.1</version>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-spring</artifactId>
            <version>1.2.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
            <version>${org.springframework-version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aop</artifactId>
            <version>${org.springframework-version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-beans</artifactId>
            <version>${org.springframework-version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
            <version>${org.springframework-version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
            <version>${org.springframework-version}</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-expression</artifactId>
            <version>${org.springframework-version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
            <version>${org.springframework-version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-redis</artifactId>
            <version>1.5.0.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>net.sf.ehcache</groupId>
            <artifactId>ehcache-core</artifactId>
            <version>2.6.10</version>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
            <version>1.7.2</version>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.9</version>
        </dependency>
        <dependency>
            <groupId>org.jdom</groupId>
            <artifactId>jdom</artifactId>
            <version>1.1</version>
        </dependency>

        <dependency>
            <groupId>com.github.kevinsawicki</groupId>
            <artifactId>http-request</artifactId>
            <version>6.0</version>
        </dependency>

        <!--spring test-->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <version>${org.springframework-version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.jayway.jsonpath</groupId>
            <artifactId>json-path</artifactId>
            <version>2.0.0</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.jayway.restassured</groupId>
            <artifactId>rest-assured</artifactId>
            <version>2.8.0</version>
            <scope>test</scope>
        </dependency>

        <!--filter zip-->
        <dependency>
            <groupId>com.github.ziplet</groupId>
            <artifactId>ziplet</artifactId>
            <version>2.0.0</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-nop</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--rest exception-->
        <dependency>
            <groupId>cz.jirutka.spring</groupId>
            <artifactId>spring-rest-exception-handler</artifactId>
            <version>1.2.0</version>
        </dependency>

        <!--convert-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>toolkit.common</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.toolkit</groupId>
            <artifactId>toolkit-common-convert</artifactId>
            <version>1.0</version>
        </dependency>

        <!--test-->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-core</artifactId>
            <version>1.3</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-library</artifactId>
            <version>1.3</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.testng</groupId>
            <artifactId>testng</artifactId>
            <version>6.8.8</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>qrcode-client</artifactId>
            <version>${qrcode-client-version}</version>
        </dependency>

        <!-- http -->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.3</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore</artifactId>
            <version>4.4.6</version>
        </dependency>

        <!-- trade -->
        <!-- 国际化 -->
        <dependency>
            <groupId>com.dfire</groupId>
            <artifactId>i18n</artifactId>
            <version>${i18n.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>spring-beans</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-core</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>multi-filter</artifactId>
            <version>${multi-filter-version}</version>
        </dependency>
        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>multi-client</artifactId>
            <version>${multi-client-version}</version>
        </dependency>

        <dependency>
            <groupId>com.dfire</groupId>
            <artifactId>dfire-I18n</artifactId>
            <version>${dfire-I18n.version}</version>
        </dependency>

        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>multi-converter</artifactId>
            <version>${multi-converter.version}</version>
        </dependency>

        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <version>4.0.0</version>
        </dependency>

        <dependency>
            <groupId>org.easymock</groupId>
            <artifactId>easymock</artifactId>
            <version>3.4</version>
            <scope>test</scope>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.dfire.open.api</groupId>-->
<!--            <artifactId>open-client</artifactId>-->
<!--            <version>0.0.20</version>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <artifactId>shop-member-client</artifactId>-->
<!--                    <groupId>com.dfire.soa</groupId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <artifactId>fastjson</artifactId>-->
<!--                    <groupId>com.alibaba</groupId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <artifactId>dubbo</artifactId>-->
<!--                    <groupId>com.alibaba</groupId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.twodfire</groupId>
            <artifactId>twodfire-session</artifactId>
            <version>${twodfire-session-version}</version>
        </dependency>

        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>thirdbind-client</artifactId>
            <version>1.0.82</version>
        </dependency>

        <!--黑名单风控业务-->
        <dependency>
            <groupId>com.dfire</groupId>
            <artifactId>counsellor-client</artifactId>
            <version>1.0.1</version>
        </dependency>

        <!--兑吧java工具包 -->
        <dependency>
            <groupId>cn.com.duiba.credits</groupId>
            <artifactId>duiba-java-sdk</artifactId>
            <version>0.0.14</version>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>servlet-api</artifactId>
                    <groupId>javax.servlet</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>net.sf.dozer</groupId>
            <artifactId>dozer</artifactId>
            <version>5.5.1</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.47</version>
        </dependency>

        <!-- 物流依赖 -->
        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>lp-client</artifactId>
            <version>1.0.2</version>
            <exclusions>
                <exclusion>
                    <artifactId>shop-client</artifactId>
                    <groupId>com.dfire.soa</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
            <version>1.5.7.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-transport-simple-http</artifactId>
            <version>${sentinel-version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-core</artifactId>
            <version>${sentinel-version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-datasource-zookeeper</artifactId>
            <version>${sentinel-version}</version>
        </dependency>
        <!--零售退款-->
        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>refund-client</artifactId>
            <version>${refund-client-version}</version>
        </dependency>

        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>retail-common-client</artifactId>
            <version>${retail-common-client-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>item-platform-client</artifactId>
                    <groupId>com.dfire.soa</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>merchant-client</artifactId>
            <version>${merchant-client-version}</version>
        </dependency>
        <!--google api-->
        <dependency>
            <groupId>com.google.api-client</groupId>
            <artifactId>google-api-client</artifactId>
            <version>1.31.2</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.google.apis/google-api-services-admin-directory -->
        <dependency>
            <groupId>com.google.apis</groupId>
            <artifactId>google-api-services-admin-directory</artifactId>
            <version>directory_v1-rev118-1.25.0</version>
        </dependency>

        <dependency>
            <groupId>com.dfire.bp</groupId>
            <artifactId>operate-center-client</artifactId>
            <version>1.1.8</version>
        </dependency>

        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>reserve-client</artifactId>
            <version>${reserve-client.version}</version>
        </dependency>

        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>invoice-client</artifactId>
            <version>${invoice-client.version}</version>
        </dependency>

        <!--bytedance-tool-->
        <dependency>
            <groupId>com.dfire.tt</groupId>
            <artifactId>tt-mp</artifactId>
            <version>${bytedance-mp.version}</version>
        </dependency>
        <!-- 渔火scrm-client -->
        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>wework-scrm-client</artifactId>
            <version>${wework-scrm-client.version}</version>
        </dependency>

        <!-- config center soa -->
        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>config-center-client</artifactId>
            <version>1.0.2</version>
        </dependency>


        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool.version}</version>
        </dependency>

        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
            <version>2.0.1.Final</version>
        </dependency>
        <dependency>
            <artifactId>open-util</artifactId>
            <groupId>com.dfire.open.api</groupId>
        </dependency>
        <dependency>
            <groupId>com.dfire.soa</groupId>
            <artifactId>seat-status-client</artifactId>
            <version>${seat-status.version}</version>
        </dependency>

    </dependencies>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.dfire.tc</groupId>
                <artifactId>tc-conf-client</artifactId>
                <version>${tc-conf-client.version}</version>
            </dependency>
            <dependency>
                <artifactId>dfire-util</artifactId>
                <groupId>com.dfire</groupId>
                <version>${dfire-util.version}</version>
            </dependency>
            <dependency>
                <groupId>com.twodfire</groupId>
                <artifactId>dfire-result</artifactId>
                <version>${dfire-result.version}</version>
            </dependency>
            <dependency>
                <artifactId>open-util</artifactId>
                <groupId>com.dfire.open.api</groupId>
                <version>1.0.83</version>
            </dependency>
            <!-- 废弃的包，避免冲突才定义 -->
            <dependency>
                <artifactId>dfire-util</artifactId>
                <groupId>com.twodfire</groupId>
                <version>1.1.0</version>
            </dependency>
            <!-- 废弃的包，避免冲突才定义 -->
            <dependency>
                <artifactId>twodfire-util</artifactId>
                <groupId>com.twodfire</groupId>
                <version>1.5.15</version>
            </dependency>
        </dependencies>
    </dependencyManagement>


    <repositories>
        <repository>
            <id>sonatype-snapshots</id>
            <name>Sonatype repository for deploying snapshots</name>
            <url>https://oss.sonatype.org/content/repositories/snapshots</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

    <build>
        <finalName>online-wechat</finalName>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.sonarsource.scanner.maven</groupId>
                    <artifactId>sonar-maven-plugin</artifactId>
                    <dependencies>
                        <dependency>
                            <groupId>javax.servlet</groupId>
                            <artifactId>javax.servlet-api</artifactId>
                            <version>4.0.1</version>
                        </dependency>
                    </dependencies>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <artifactId>maven-dependency-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.19</version>
                <configuration>
                    <useSystemClassLoader>false</useSystemClassLoader>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.0.1</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>2.4</version>
                <configuration>
                    <descriptor>src/main/assembly/assembly.xml</descriptor>
                    <finalName>weixin-meal</finalName>
                    <appendAssemblyId>false</appendAssemblyId>
                    <outputDirectory>./</outputDirectory>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>3.3.2</version>
                <configuration>
                    <webResources>
                        <resource>
                            <directory>src/main/webapp/WEB-INF</directory>
                            <filtering>true</filtering>
                            <includes>
                                <include>**/*.properties</include>
                                <include>**/*.xml</include>
                            </includes>
                            <targetPath>WEB-INF</targetPath>
                        </resource>
                    </webResources>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-maven-plugin</artifactId>
                <version>9.4.9.v20180320</version>
                <configuration>
                    <!--<webAppSourceDirectory>${project.build.directory}/waiterservice-api</webAppSourceDirectory>-->
                    <scanIntervalSeconds>3</scanIntervalSeconds>
                    <stopKey>foo</stopKey>
                    <stopPort>9999</stopPort>
                    <!-- make sure Jetty also finds the widgetset -->
                    <!--<webAppConfig>-->
                    <!--<contextPath>/</contextPath>-->
                    <!--<baseResource implementation="org.mortbay.resource.ResourceCollection">-->
                    <!--<resourcesAsCSV>-->
                    <!--${project.build.directory}/${project.build.finalName}-->
                    <!--</resourcesAsCSV>-->
                    <!--</baseResource>-->
                    <!--</webAppConfig>-->
                </configuration>
                <executions>
                    <execution>
                        <id>start-jetty</id>
                        <phase>pre-integration-test</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <scanIntervalSeconds>0</scanIntervalSeconds>
                        </configuration>
                    </execution>
                    <execution>
                        <id>stop-jetty</id>
                        <phase>post-integration-test</phase>
                        <goals>
                            <goal>stop</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.7</version>
                <executions>
                    <execution>
                        <id>prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <filters>
            <filter>src/main/filter/${env}.properties</filter>
        </filters>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/**</include>
                </includes>
            </resource>
        </resources>
    </build>
    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <env>dev</env>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.thetransactioncompany</groupId>
                    <artifactId>cors-filter</artifactId>
                    <version>2.4</version>
                </dependency>
                <dependency>
                    <groupId>com.thetransactioncompany</groupId>
                    <artifactId>java-property-utils</artifactId>
                    <version>1.9.1</version>
                </dependency>
            </dependencies>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>daily</id>
            <properties>
                <env>daily</env>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.thetransactioncompany</groupId>
                    <artifactId>cors-filter</artifactId>
                    <version>2.4</version>
                </dependency>
                <dependency>
                    <groupId>com.thetransactioncompany</groupId>
                    <artifactId>java-property-utils</artifactId>
                    <version>1.9.1</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>pre</id>
            <properties>
                <env>pre</env>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.thetransactioncompany</groupId>
                    <artifactId>cors-filter</artifactId>
                    <version>2.4</version>
                </dependency>
                <dependency>
                    <groupId>com.thetransactioncompany</groupId>
                    <artifactId>java-property-utils</artifactId>
                    <version>1.9.1</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>publish</id>
            <properties>
                <env>publish</env>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.thetransactioncompany</groupId>
                    <artifactId>cors-filter</artifactId>
                    <version>2.4</version>
                </dependency>
                <dependency>
                    <groupId>com.thetransactioncompany</groupId>
                    <artifactId>java-property-utils</artifactId>
                    <version>1.9.1</version>
                </dependency>
            </dependencies>
        </profile>
    </profiles>

</project>
